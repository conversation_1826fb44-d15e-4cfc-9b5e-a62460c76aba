#!/bin/bash

echo "================================================"
echo "           نظام إدارة الملفات"
echo "        File Management System"
echo "================================================"
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python3 غير مثبت"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

echo "تم العثور على Python3..."
echo

# إنشاء البيئة الافتراضية إذا لم تكن موجودة
if [ ! -d "venv" ]; then
    echo "إنشاء البيئة الافتراضية..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "فشل في إنشاء البيئة الافتراضية"
        exit 1
    fi
fi

# تفعيل البيئة الافتراضية
echo "تفعيل البيئة الافتراضية..."
source venv/bin/activate

# تثبيت المتطلبات
echo "تثبيت المتطلبات..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "فشل في تثبيت المتطلبات"
    exit 1
fi

# إنشاء المجلدات المطلوبة
mkdir -p uploads
mkdir -p instance

echo
echo "================================================"
echo "تشغيل النظام..."
echo "الرابط: http://localhost:5000"
echo "المدير الافتراضي:"
echo "  اسم المستخدم: admin"
echo "  كلمة المرور: admin123"
echo "  القسم: الإدارة"
echo "================================================"
echo "للإيقاف: اضغط Ctrl+C"
echo "================================================"
echo

# تشغيل التطبيق
python3 app.py
