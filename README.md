# نظام إدارة الملفات

نظام متكامل لإدارة الملفات باستخدام Flask و Bootstrap مع دعم اللغة العربية.

## المميزات

### للمديرين
- **لوحة تحكم شاملة** مع إحصائيات مفصلة
- **إدارة الموظفين** - إضافة، تعديل، حذف الموظفين
- **إدارة الأقسام** - إنشاء وتنظيم الأقسام الوظيفية
- **مراقبة جميع الملفات** مع إمكانية البحث والتصفية المتقدمة
- **تصدير التقارير** بصيغة CSV
- **إحصائيات متقدمة** حول استخدام النظام

### للموظفين
- **رفع الملفات** مع دعم السحب والإفلات
- **إدارة الملفات الشخصية** - عرض، تعديل، حذف
- **مشاركة الملفات** كملفات عامة أو خاصة
- **البحث في الملفات** مع فلاتر متقدمة
- **عرض الملفات العامة** من الأقسام الأخرى

### المميزات التقنية
- **واجهة مستخدم عربية** مع دعم RTL
- **تصميم متجاوب** باستخدام Bootstrap 5
- **أمان متقدم** مع تشفير كلمات المرور
- **رفع ملفات آمن** مع التحقق من الأنواع والأحجام
- **قاعدة بيانات SQLite** سهلة النشر
- **بحث متقدم** مع فلاتر متعددة

## متطلبات النظام

- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- مساحة تخزين كافية للملفات المرفوعة

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd file-management-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# على Windows
venv\Scripts\activate

# على Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل النظام
```bash
python app.py
```

### 5. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## بيانات تسجيل الدخول الافتراضية

### المدير
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **القسم:** الإدارة

## هيكل المشروع

```
file-management-system/
├── app/
│   ├── admin/              # واجهات المدير
│   ├── auth/               # نظام المصادقة
│   ├── employee/           # واجهات الموظف
│   ├── main/               # الصفحات الرئيسية
│   ├── static/             # الملفات الثابتة
│   │   ├── css/
│   │   └── js/
│   ├── templates/          # قوالب HTML
│   ├── models.py           # نماذج قاعدة البيانات
│   └── utils.py            # وظائف مساعدة
├── uploads/                # مجلد الملفات المرفوعة
├── app.py                  # ملف التطبيق الرئيسي
├── config.py               # إعدادات التطبيق
├── requirements.txt        # متطلبات Python
└── README.md              # هذا الملف
```

## الإعدادات

يمكن تخصيص النظام من خلال ملف `config.py`:

- **SECRET_KEY**: مفتاح التشفير (يجب تغييره في الإنتاج)
- **UPLOAD_FOLDER**: مجلد حفظ الملفات
- **MAX_CONTENT_LENGTH**: الحد الأقصى لحجم الملف (16MB افتراضياً)
- **ALLOWED_EXTENSIONS**: أنواع الملفات المسموحة

## أنواع الملفات المدعومة

- **المستندات:** PDF, DOC, DOCX, TXT, RTF
- **الصور:** PNG, JPG, JPEG, GIF, BMP, SVG
- **جداول البيانات:** XLS, XLSX, CSV
- **العروض التقديمية:** PPT, PPTX
- **الملفات المضغوطة:** ZIP, RAR, 7Z, TAR, GZ

## الأمان

- تشفير كلمات المرور باستخدام Werkzeug
- التحقق من أنواع الملفات المرفوعة
- حماية من رفع الملفات الضارة
- جلسات آمنة مع انتهاء صلاحية
- صلاحيات مختلفة للمديرين والموظفين

## النشر في الإنتاج

### 1. تغيير الإعدادات
```python
# في config.py
SECRET_KEY = 'your-production-secret-key'
DEBUG = False
```

### 2. استخدام خادم ويب
```bash
# باستخدام Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

### 3. قاعدة بيانات خارجية (اختياري)
```python
# في config.py
SQLALCHEMY_DATABASE_URI = 'mysql://user:password@localhost/dbname'
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تثبيت المتطلبات**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

2. **مشكلة في الصلاحيات**
   ```bash
   chmod 755 uploads/
   ```

3. **خطأ في قاعدة البيانات**
   - احذف ملف `file_management.db` وأعد تشغيل التطبيق

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم:
- افتح Issue في GitHub
- راجع الوثائق
- تحقق من الأخطاء الشائعة أعلاه

## التحديثات المستقبلية

- [ ] دعم قواعد بيانات متعددة
- [ ] واجهة برمجة تطبيقات REST API
- [ ] نظام إشعارات
- [ ] تقارير متقدمة
- [ ] دعم اللغات المتعددة
- [ ] تطبيق جوال

---

تم تطوير هذا النظام باستخدام Flask و Bootstrap مع التركيز على سهولة الاستخدام والأمان.
