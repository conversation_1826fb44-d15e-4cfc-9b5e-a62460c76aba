from flask import render_template, redirect, url_for, flash, request, jsonify, send_file, abort
from flask_login import login_required, current_user
from sqlalchemy import or_, desc
from datetime import datetime
from app.admin import bp
from app.admin.forms import DepartmentForm, UserForm, EditUserForm, SearchForm
from app.models import Department, User, File
from app.utils import admin_required
from app import db
import os

@bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """لوحة تحكم المدير"""
    # إحصائيات عامة
    stats = {
        'total_users': User.query.count(),
        'total_departments': Department.query.count(),
        'total_files': File.query.count(),
        'active_users': User.query.filter_by(is_active=True).count()
    }
    
    # أحدث الملفات المرفوعة
    recent_files = File.query.order_by(desc(File.uploaded_at)).limit(10).all()
    
    # أحدث المستخدمين المسجلين
    recent_users = User.query.order_by(desc(User.created_at)).limit(5).all()
    
    return render_template('admin/dashboard.html', 
                         stats=stats, 
                         recent_files=recent_files,
                         recent_users=recent_users)

# إدارة الأقسام
@bp.route('/departments')
@login_required
@admin_required
def departments():
    """عرض جميع الأقسام"""
    departments = Department.query.all()
    return render_template('admin/departments.html', departments=departments)

@bp.route('/departments/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_department():
    """إضافة قسم جديد"""
    form = DepartmentForm()
    if form.validate_on_submit():
        # التحقق من عدم وجود قسم بنفس الاسم
        existing_dept = Department.query.filter_by(name=form.name.data).first()
        if existing_dept:
            flash('يوجد قسم بهذا الاسم مسبقاً', 'error')
            return render_template('admin/add_department.html', form=form)
        
        department = Department(
            name=form.name.data,
            description=form.description.data
        )
        db.session.add(department)
        db.session.commit()
        flash('تم إضافة القسم بنجاح', 'success')
        return redirect(url_for('admin.departments'))
    
    return render_template('admin/add_department.html', form=form)

@bp.route('/departments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_department(id):
    """تعديل قسم"""
    department = Department.query.get_or_404(id)
    form = DepartmentForm(obj=department)
    
    if form.validate_on_submit():
        # التحقق من عدم وجود قسم آخر بنفس الاسم
        existing_dept = Department.query.filter(
            Department.name == form.name.data,
            Department.id != id
        ).first()
        if existing_dept:
            flash('يوجد قسم آخر بهذا الاسم', 'error')
            return render_template('admin/edit_department.html', form=form, department=department)
        
        department.name = form.name.data
        department.description = form.description.data
        db.session.commit()
        flash('تم تحديث القسم بنجاح', 'success')
        return redirect(url_for('admin.departments'))
    
    return render_template('admin/edit_department.html', form=form, department=department)

@bp.route('/departments/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_department(id):
    """حذف قسم"""
    department = Department.query.get_or_404(id)

    # التحقق من عدم وجود موظفين في هذا القسم
    if department.users.count() > 0:
        flash('لا يمكن حذف القسم لأنه يحتوي على موظفين', 'error')
        return redirect(url_for('admin.departments'))

    db.session.delete(department)
    db.session.commit()
    flash('تم حذف القسم بنجاح', 'success')
    return redirect(url_for('admin.departments'))

# إدارة المستخدمين
@bp.route('/users')
@login_required
@admin_required
def users():
    """عرض جميع المستخدمين"""
    page = request.args.get('page', 1, type=int)
    department_id = request.args.get('department', 0, type=int)

    query = User.query
    if department_id:
        query = query.filter_by(department_id=department_id)

    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    departments = Department.query.all()
    return render_template('admin/users.html', users=users, departments=departments,
                         selected_department=department_id)

@bp.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    form = UserForm()
    if form.validate_on_submit():
        # التحقق من عدم وجود مستخدم بنفس اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter(
            or_(User.username == form.username.data, User.email == form.email.data)
        ).first()
        if existing_user:
            flash('يوجد مستخدم بنفس اسم المستخدم أو البريد الإلكتروني', 'error')
            return render_template('admin/add_user.html', form=form)

        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data,
            department_id=form.department_id.data,
            is_admin=form.is_admin.data,
            is_active=form.is_active.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('admin.users'))

    return render_template('admin/add_user.html', form=form)

@bp.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(id):
    """تعديل مستخدم"""
    user = User.query.get_or_404(id)
    form = EditUserForm(obj=user)

    if form.validate_on_submit():
        # التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter(
            or_(User.username == form.username.data, User.email == form.email.data),
            User.id != id
        ).first()
        if existing_user:
            flash('يوجد مستخدم آخر بنفس اسم المستخدم أو البريد الإلكتروني', 'error')
            return render_template('admin/edit_user.html', form=form, user=user)

        user.username = form.username.data
        user.email = form.email.data
        user.full_name = form.full_name.data
        user.department_id = form.department_id.data
        user.is_admin = form.is_admin.data
        user.is_active = form.is_active.data

        # تحديث كلمة المرور إذا تم إدخالها
        if form.password.data:
            user.set_password(form.password.data)

        db.session.commit()
        flash('تم تحديث المستخدم بنجاح', 'success')
        return redirect(url_for('admin.users'))

    return render_template('admin/edit_user.html', form=form, user=user)

@bp.route('/users/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(id):
    """حذف مستخدم"""
    user = User.query.get_or_404(id)

    # منع حذف المدير الحالي لنفسه
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('admin.users'))

    # حذف ملفات المستخدم أولاً
    for file in user.files:
        try:
            if os.path.exists(file.file_path):
                os.remove(file.file_path)
        except:
            pass
        db.session.delete(file)

    db.session.delete(user)
    db.session.commit()
    flash('تم حذف المستخدم وجميع ملفاته بنجاح', 'success')
    return redirect(url_for('admin.users'))

# إدارة الملفات
@bp.route('/files')
@login_required
@admin_required
def files():
    """عرض جميع الملفات مع البحث والتصفية المتقدمة"""
    page = request.args.get('page', 1, type=int)
    search_form = SearchForm()

    query = File.query.join(User).join(Department)

    # تطبيق فلتر البحث النصي
    search_query = request.args.get('query', '').strip()
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                File.title.like(search_term),
                File.description.like(search_term),
                File.original_filename.like(search_term),
                User.full_name.like(search_term),
                Department.name.like(search_term)
            )
        )

    # تطبيق فلتر القسم
    department_id = request.args.get('department_id', type=int)
    if department_id and department_id > 0:
        query = query.filter(File.department_id == department_id)

    # تطبيق فلتر نوع الملف
    file_type = request.args.get('file_type', '').strip()
    if file_type:
        query = query.filter(File.file_type == file_type)

    # تطبيق فلتر الموظف
    uploader_id = request.args.get('uploader_id', type=int)
    if uploader_id:
        query = query.filter(File.uploader_id == uploader_id)

    # تطبيق فلتر الملفات العامة/الخاصة
    is_public = request.args.get('is_public')
    if is_public == '1':
        query = query.filter(File.is_public == True)
    elif is_public == '0':
        query = query.filter(File.is_public == False)

    # تطبيق فلتر التاريخ
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    if date_from:
        try:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(File.uploaded_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # إضافة 24 ساعة للتاريخ النهائي
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(File.uploaded_at <= date_to_obj)
        except ValueError:
            pass

    # ترتيب النتائج
    sort_by = request.args.get('sort_by', 'uploaded_at')
    sort_order = request.args.get('sort_order', 'desc')

    if sort_by == 'title':
        order_column = File.title
    elif sort_by == 'size':
        order_column = File.file_size
    elif sort_by == 'uploader':
        order_column = User.full_name
    elif sort_by == 'department':
        order_column = Department.name
    else:
        order_column = File.uploaded_at

    if sort_order == 'asc':
        query = query.order_by(order_column.asc())
    else:
        query = query.order_by(order_column.desc())

    files = query.paginate(page=page, per_page=20, error_out=False)

    # إحصائيات البحث
    search_stats = {
        'total_files': query.count(),
        'total_size': sum([f.file_size or 0 for f in query.all()]),
        'file_types': db.session.query(File.file_type, db.func.count(File.id)).filter(
            File.id.in_([f.id for f in query.all()])
        ).group_by(File.file_type).all() if search_query or department_id or file_type else []
    }

    return render_template('admin/files.html',
                         files=files,
                         search_form=search_form,
                         search_stats=search_stats,
                         current_filters={
                             'query': search_query,
                             'department_id': department_id,
                             'file_type': file_type,
                             'uploader_id': uploader_id,
                             'is_public': is_public,
                             'date_from': date_from,
                             'date_to': date_to,
                             'sort_by': sort_by,
                             'sort_order': sort_order
                         })

@bp.route('/files/<int:id>/download')
@login_required
@admin_required
def download_file(id):
    """تحميل ملف"""
    file = File.query.get_or_404(id)

    if not os.path.exists(file.file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('admin.files'))

    file.increment_download_count()
    return send_file(file.file_path, as_attachment=True,
                    download_name=file.original_filename)

@bp.route('/files/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_file(id):
    """حذف ملف"""
    file = File.query.get_or_404(id)

    # حذف الملف من النظام
    try:
        if os.path.exists(file.file_path):
            os.remove(file.file_path)
    except Exception as e:
        flash(f'خطأ في حذف الملف: {str(e)}', 'error')
        return redirect(url_for('admin.files'))

    # حذف السجل من قاعدة البيانات
    db.session.delete(file)
    db.session.commit()
    flash('تم حذف الملف بنجاح', 'success')
    return redirect(url_for('admin.files'))

# API Routes for Search
@bp.route('/api/search-files')
@login_required
@admin_required
def api_search_files():
    """API للبحث السريع في الملفات"""
    query = request.args.get('q', '').strip()
    limit = request.args.get('limit', 10, type=int)

    if not query or len(query) < 2:
        return jsonify({'files': []})

    search_term = f"%{query}%"
    files = File.query.join(User).join(Department).filter(
        or_(
            File.title.like(search_term),
            File.original_filename.like(search_term),
            User.full_name.like(search_term),
            Department.name.like(search_term)
        )
    ).limit(limit).all()

    results = []
    for file in files:
        results.append({
            'id': file.id,
            'title': file.title,
            'filename': file.original_filename,
            'uploader': file.uploader.full_name,
            'department': file.department.name,
            'size': file.get_file_size_formatted(),
            'type': file.file_type,
            'uploaded_at': file.uploaded_at.strftime('%Y-%m-%d'),
            'download_url': url_for('admin.download_file', id=file.id)
        })

    return jsonify({'files': results})

@bp.route('/api/file-stats')
@login_required
@admin_required
def api_file_stats():
    """API لإحصائيات الملفات"""
    # إحصائيات عامة
    total_files = File.query.count()
    total_size = sum([f.file_size or 0 for f in File.query.all()])

    # إحصائيات حسب النوع
    file_types = db.session.query(
        File.file_type,
        db.func.count(File.id).label('count'),
        db.func.sum(File.file_size).label('total_size')
    ).group_by(File.file_type).all()

    # إحصائيات حسب القسم
    dept_stats = db.session.query(
        Department.name,
        db.func.count(File.id).label('count'),
        db.func.sum(File.file_size).label('total_size')
    ).join(File).group_by(Department.name).all()

    # إحصائيات حسب الشهر (آخر 12 شهر)
    from datetime import datetime, timedelta
    twelve_months_ago = datetime.utcnow() - timedelta(days=365)
    monthly_stats = db.session.query(
        db.func.strftime('%Y-%m', File.uploaded_at).label('month'),
        db.func.count(File.id).label('count')
    ).filter(File.uploaded_at >= twelve_months_ago).group_by(
        db.func.strftime('%Y-%m', File.uploaded_at)
    ).all()

    return jsonify({
        'total_files': total_files,
        'total_size': total_size,
        'file_types': [{'type': ft[0], 'count': ft[1], 'size': ft[2] or 0} for ft in file_types],
        'departments': [{'name': ds[0], 'count': ds[1], 'size': ds[2] or 0} for ds in dept_stats],
        'monthly': [{'month': ms[0], 'count': ms[1]} for ms in monthly_stats]
    })

@bp.route('/export/files')
@login_required
@admin_required
def export_files():
    """تصدير قائمة الملفات إلى CSV"""
    import csv
    from io import StringIO
    from flask import make_response

    # الحصول على الملفات مع الفلاتر المطبقة
    query = File.query.join(User).join(Department)

    # تطبيق نفس الفلاتر المستخدمة في صفحة الملفات
    search_query = request.args.get('query', '').strip()
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                File.title.like(search_term),
                File.description.like(search_term),
                File.original_filename.like(search_term),
                User.full_name.like(search_term),
                Department.name.like(search_term)
            )
        )

    department_id = request.args.get('department_id', type=int)
    if department_id and department_id > 0:
        query = query.filter(File.department_id == department_id)

    file_type = request.args.get('file_type', '').strip()
    if file_type:
        query = query.filter(File.file_type == file_type)

    files = query.order_by(File.uploaded_at.desc()).all()

    # إنشاء ملف CSV
    output = StringIO()
    writer = csv.writer(output)

    # كتابة العناوين
    writer.writerow([
        'العنوان', 'اسم الملف الأصلي', 'الموظف', 'القسم',
        'نوع الملف', 'الحجم (بايت)', 'عام/خاص', 'تاريخ الرفع', 'عدد التحميلات'
    ])

    # كتابة البيانات
    for file in files:
        writer.writerow([
            file.title,
            file.original_filename,
            file.uploader.full_name,
            file.department.name,
            file.file_type,
            file.file_size or 0,
            'عام' if file.is_public else 'خاص',
            file.uploaded_at.strftime('%Y-%m-%d %H:%M:%S'),
            file.download_count
        ])

    # إنشاء الاستجابة
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename=files_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

    return response
