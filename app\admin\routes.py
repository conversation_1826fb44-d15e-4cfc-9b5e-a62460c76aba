from flask import render_template, redirect, url_for, flash, request, jsonify, send_file, abort
from flask_login import login_required, current_user
from sqlalchemy import or_, desc
from app.admin import bp
from app.admin.forms import DepartmentForm, UserForm, EditUserForm, SearchForm
from app.models import Department, User, File
from app.utils import admin_required
from app import db
import os

@bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """لوحة تحكم المدير"""
    # إحصائيات عامة
    stats = {
        'total_users': User.query.count(),
        'total_departments': Department.query.count(),
        'total_files': File.query.count(),
        'active_users': User.query.filter_by(is_active=True).count()
    }
    
    # أحدث الملفات المرفوعة
    recent_files = File.query.order_by(desc(File.uploaded_at)).limit(10).all()
    
    # أحدث المستخدمين المسجلين
    recent_users = User.query.order_by(desc(User.created_at)).limit(5).all()
    
    return render_template('admin/dashboard.html', 
                         stats=stats, 
                         recent_files=recent_files,
                         recent_users=recent_users)

# إدارة الأقسام
@bp.route('/departments')
@login_required
@admin_required
def departments():
    """عرض جميع الأقسام"""
    departments = Department.query.all()
    return render_template('admin/departments.html', departments=departments)

@bp.route('/departments/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_department():
    """إضافة قسم جديد"""
    form = DepartmentForm()
    if form.validate_on_submit():
        # التحقق من عدم وجود قسم بنفس الاسم
        existing_dept = Department.query.filter_by(name=form.name.data).first()
        if existing_dept:
            flash('يوجد قسم بهذا الاسم مسبقاً', 'error')
            return render_template('admin/add_department.html', form=form)
        
        department = Department(
            name=form.name.data,
            description=form.description.data
        )
        db.session.add(department)
        db.session.commit()
        flash('تم إضافة القسم بنجاح', 'success')
        return redirect(url_for('admin.departments'))
    
    return render_template('admin/add_department.html', form=form)

@bp.route('/departments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_department(id):
    """تعديل قسم"""
    department = Department.query.get_or_404(id)
    form = DepartmentForm(obj=department)
    
    if form.validate_on_submit():
        # التحقق من عدم وجود قسم آخر بنفس الاسم
        existing_dept = Department.query.filter(
            Department.name == form.name.data,
            Department.id != id
        ).first()
        if existing_dept:
            flash('يوجد قسم آخر بهذا الاسم', 'error')
            return render_template('admin/edit_department.html', form=form, department=department)
        
        department.name = form.name.data
        department.description = form.description.data
        db.session.commit()
        flash('تم تحديث القسم بنجاح', 'success')
        return redirect(url_for('admin.departments'))
    
    return render_template('admin/edit_department.html', form=form, department=department)

@bp.route('/departments/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_department(id):
    """حذف قسم"""
    department = Department.query.get_or_404(id)

    # التحقق من عدم وجود موظفين في هذا القسم
    if department.users.count() > 0:
        flash('لا يمكن حذف القسم لأنه يحتوي على موظفين', 'error')
        return redirect(url_for('admin.departments'))

    db.session.delete(department)
    db.session.commit()
    flash('تم حذف القسم بنجاح', 'success')
    return redirect(url_for('admin.departments'))

# إدارة المستخدمين
@bp.route('/users')
@login_required
@admin_required
def users():
    """عرض جميع المستخدمين"""
    page = request.args.get('page', 1, type=int)
    department_id = request.args.get('department', 0, type=int)

    query = User.query
    if department_id:
        query = query.filter_by(department_id=department_id)

    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    departments = Department.query.all()
    return render_template('admin/users.html', users=users, departments=departments,
                         selected_department=department_id)

@bp.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    form = UserForm()
    if form.validate_on_submit():
        # التحقق من عدم وجود مستخدم بنفس اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter(
            or_(User.username == form.username.data, User.email == form.email.data)
        ).first()
        if existing_user:
            flash('يوجد مستخدم بنفس اسم المستخدم أو البريد الإلكتروني', 'error')
            return render_template('admin/add_user.html', form=form)

        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data,
            department_id=form.department_id.data,
            is_admin=form.is_admin.data,
            is_active=form.is_active.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('admin.users'))

    return render_template('admin/add_user.html', form=form)

@bp.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(id):
    """تعديل مستخدم"""
    user = User.query.get_or_404(id)
    form = EditUserForm(obj=user)

    if form.validate_on_submit():
        # التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter(
            or_(User.username == form.username.data, User.email == form.email.data),
            User.id != id
        ).first()
        if existing_user:
            flash('يوجد مستخدم آخر بنفس اسم المستخدم أو البريد الإلكتروني', 'error')
            return render_template('admin/edit_user.html', form=form, user=user)

        user.username = form.username.data
        user.email = form.email.data
        user.full_name = form.full_name.data
        user.department_id = form.department_id.data
        user.is_admin = form.is_admin.data
        user.is_active = form.is_active.data

        # تحديث كلمة المرور إذا تم إدخالها
        if form.password.data:
            user.set_password(form.password.data)

        db.session.commit()
        flash('تم تحديث المستخدم بنجاح', 'success')
        return redirect(url_for('admin.users'))

    return render_template('admin/edit_user.html', form=form, user=user)

@bp.route('/users/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(id):
    """حذف مستخدم"""
    user = User.query.get_or_404(id)

    # منع حذف المدير الحالي لنفسه
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('admin.users'))

    # حذف ملفات المستخدم أولاً
    for file in user.files:
        try:
            if os.path.exists(file.file_path):
                os.remove(file.file_path)
        except:
            pass
        db.session.delete(file)

    db.session.delete(user)
    db.session.commit()
    flash('تم حذف المستخدم وجميع ملفاته بنجاح', 'success')
    return redirect(url_for('admin.users'))

# إدارة الملفات
@bp.route('/files')
@login_required
@admin_required
def files():
    """عرض جميع الملفات"""
    page = request.args.get('page', 1, type=int)
    search_form = SearchForm()

    query = File.query

    # تطبيق الفلاتر
    if request.args.get('query'):
        search_term = f"%{request.args.get('query')}%"
        query = query.filter(
            or_(
                File.title.like(search_term),
                File.description.like(search_term),
                File.original_filename.like(search_term)
            )
        )

    if request.args.get('department_id', type=int):
        query = query.filter_by(department_id=request.args.get('department_id', type=int))

    if request.args.get('file_type'):
        query = query.filter_by(file_type=request.args.get('file_type'))

    files = query.order_by(File.uploaded_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/files.html', files=files, search_form=search_form)

@bp.route('/files/<int:id>/download')
@login_required
@admin_required
def download_file(id):
    """تحميل ملف"""
    file = File.query.get_or_404(id)

    if not os.path.exists(file.file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('admin.files'))

    file.increment_download_count()
    return send_file(file.file_path, as_attachment=True,
                    download_name=file.original_filename)

@bp.route('/files/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_file(id):
    """حذف ملف"""
    file = File.query.get_or_404(id)

    # حذف الملف من النظام
    try:
        if os.path.exists(file.file_path):
            os.remove(file.file_path)
    except Exception as e:
        flash(f'خطأ في حذف الملف: {str(e)}', 'error')
        return redirect(url_for('admin.files'))

    # حذف السجل من قاعدة البيانات
    db.session.delete(file)
    db.session.commit()
    flash('تم حذف الملف بنجاح', 'success')
    return redirect(url_for('admin.files'))
