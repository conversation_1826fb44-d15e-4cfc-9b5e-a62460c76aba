{% extends "base.html" %}

{% block title %}إضافة موظف جديد - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>
        إضافة موظف جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.users') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة إلى الموظفين
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات الموظف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- Username -->
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else ""), 
                                           placeholder="أدخل اسم المستخدم") }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">اسم المستخدم يجب أن يكون فريد</div>
                        </div>
                        
                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), 
                                        placeholder="أدخل البريد الإلكتروني") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Full Name -->
                    <div class="mb-3">
                        {{ form.full_name.label(class="form-label") }}
                        {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else ""), 
                                        placeholder="أدخل الاسم الكامل") }}
                        {% if form.full_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.full_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <!-- Password -->
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), 
                                           placeholder="أدخل كلمة المرور") }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                        </div>
                        
                        <!-- Confirm Password -->
                        <div class="col-md-6 mb-3">
                            {{ form.password2.label(class="form-label") }}
                            {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else ""), 
                                            placeholder="أعد إدخال كلمة المرور") }}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Department -->
                    <div class="mb-3">
                        {{ form.department_id.label(class="form-label") }}
                        {{ form.department_id(class="form-select" + (" is-invalid" if form.department_id.errors else "")) }}
                        {% if form.department_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.department_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Options -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-check">
                                {{ form.is_admin(class="form-check-input") }}
                                {{ form.is_admin.label(class="form-check-label") }}
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                المديرون لديهم صلاحيات كاملة في النظام
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                الموظفون غير النشطين لا يمكنهم تسجيل الدخول
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.users') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Guidelines -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    إرشادات إضافة الموظفين
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>نصائح مهمة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-left text-primary me-2"></i>تأكد من صحة البريد الإلكتروني</li>
                            <li><i class="fas fa-arrow-left text-primary me-2"></i>اختر كلمة مرور قوية</li>
                            <li><i class="fas fa-arrow-left text-primary me-2"></i>حدد القسم المناسب</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt text-warning me-2"></i>الصلاحيات:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-user text-info me-2"></i><strong>موظف عادي:</strong> رفع وإدارة ملفاته فقط</li>
                            <li><i class="fas fa-user-shield text-danger me-2"></i><strong>مدير:</strong> صلاحيات كاملة في النظام</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
