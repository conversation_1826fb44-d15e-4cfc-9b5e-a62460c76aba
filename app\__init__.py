"""
Flask application package
نظام إدارة الملفات
"""

import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from config import config

# إنشاء كائنات Flask extensions
db = SQLAlchemy()
login_manager = LoginManager()

def create_app(config_name=None):
    """إنشاء وتكوين تطبيق Flask"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # تهيئة extensions
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'
    login_manager.login_message_category = 'info'

    # تسجيل blueprints
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

    from app.employee import bp as employee_bp
    app.register_blueprint(employee_bp, url_prefix='/employee')

    # تسجيل template filters
    from app.utils import get_file_icon, format_file_size
    app.jinja_env.filters['get_file_icon'] = get_file_icon
    app.jinja_env.filters['format_file_size'] = format_file_size

    # إنشاء الجداول
    with app.app_context():
        db.create_all()

        # إضافة البيانات الأساسية إذا لم تكن موجودة
        from app.models import Department, User

        if Department.query.count() == 0:
            # إضافة الأقسام الافتراضية
            departments = [
                Department(name='الإدارة', description='قسم الإدارة العامة'),
                Department(name='الموارد البشرية', description='قسم الموارد البشرية'),
                Department(name='المالية', description='قسم المالية والمحاسبة'),
                Department(name='تقنية المعلومات', description='قسم تقنية المعلومات'),
                Department(name='التسويق', description='قسم التسويق والمبيعات'),
                Department(name='العمليات', description='قسم العمليات والإنتاج')
            ]

            for dept in departments:
                db.session.add(dept)
            db.session.commit()

        # إضافة المستخدم الإداري إذا لم يكن موجوداً
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_dept = Department.query.filter_by(name='الإدارة').first()
            if admin_dept:
                from datetime import datetime
                admin_user = User(
                    username='admin',
                    full_name='مدير النظام',
                    email='<EMAIL>',
                    department_id=admin_dept.id,
                    is_admin=True,
                    is_active=True,
                    created_at=datetime.utcnow()
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()

    return app
