# 🐍 تثبيت Python - دليل سريع

## المشكلة المحددة:
```
Python was not found; run without arguments to install from the Microsoft Store
```

## 🚀 الحلول السريعة:

### الحل الأول: تثبيت من Microsoft Store (الأسرع)
1. **اضغط Windows + R**
2. **اكتب:** `ms-windows-store://pdp/?productid=9NRWMJP3717K`
3. **اضغط Enter**
4. **انقر "Get" أو "تثبيت"**
5. **انتظر انتهاء التثبيت**

### الحل الثاني: تثبيت من الموقع الرسمي
1. **اذهب إلى:** https://www.python.org/downloads/
2. **انقر "Download Python 3.12.x"**
3. **شغل الملف المحمل**
4. **✅ تأكد من تحديد "Add Python to PATH"**
5. **انقر "Install Now"**

### الحل الثالث: استخدام Chocolatey (للمطورين)
```powershell
# في PowerShell كمدير
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
choco install python
```

## ✅ التحقق من التثبيت:
```bash
python --version
# أو
python3 --version
```

## 🔄 بعد تثبيت Python:

### 1. تثبيت المكتبات:
```bash
pip install flask flask-sqlalchemy flask-login flask-wtf wtforms werkzeug
```

### 2. تشغيل النظام:
```bash
python app.py
```

### 3. فتح المتصفح:
```
http://localhost:8080
```

## 🆘 إذا استمرت المشكلة:

### إعادة تشغيل Command Prompt:
- أغلق جميع نوافذ Terminal/CMD
- افتح نافذة جديدة
- جرب `python --version`

### إضافة Python إلى PATH يدوياً:
1. **اضغط Windows + R**
2. **اكتب:** `sysdm.cpl`
3. **انقر "Environment Variables"**
4. **أضف مسار Python إلى PATH**

---

## 🎯 الخطوات التالية بعد تثبيت Python:

1. **تثبيت Python** (اختر أي حل من الأعلى)
2. **أعد فتح Terminal جديد**
3. **شغل:** `python app.py`
4. **افتح:** `http://localhost:8080`
5. **سجل دخول بـ:** admin / admin123 / الإدارة

**بعد تثبيت Python، النظام سيعمل بشكل مثالي! 🚀**
