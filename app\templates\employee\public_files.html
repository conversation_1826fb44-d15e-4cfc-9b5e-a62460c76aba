{% extends "base.html" %}

{% block title %}الملفات العامة - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-share-alt me-2"></i>
        الملفات العامة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('employee.upload_file') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>
                رفع ملف جديد
            </a>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث في الملفات العامة
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-8">
                {{ search_form.query(class="form-control", value=request.args.get('query', ''), 
                                    placeholder="البحث في الملفات العامة...") }}
            </div>
            <div class="col-md-4">
                <div class="d-grid">
                    {{ search_form.submit(class="btn btn-primary") }}
                </div>
            </div>
        </form>
    </div>
</div>

{% if files.items %}
<!-- Files Grid -->
<div class="row">
    {% for file in files.items %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="{{ file.file_type | get_file_icon }} fa-lg text-primary me-2"></i>
                    <h6 class="mb-0">{{ file.title }}</h6>
                </div>
                <span class="badge bg-success">عام</span>
            </div>
            <div class="card-body">
                <p class="card-text">
                    {% if file.description %}
                        {{ file.description[:100] }}{% if file.description|length > 100 %}...{% endif %}
                    {% else %}
                        <span class="text-muted">لا يوجد وصف</span>
                    {% endif %}
                </p>
                
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border-end">
                            <small class="text-muted d-block">الحجم</small>
                            <strong>{{ file.get_file_size_formatted() }}</strong>
                        </div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">التحميلات</small>
                        <strong>{{ file.download_count }}</strong>
                    </div>
                </div>
                
                <div class="mb-3">
                    <span class="badge bg-info file-type-badge">{{ file.file_type }}</span>
                </div>
                
                <!-- Uploader Info -->
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                        <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center w-100 h-100">
                            {{ file.uploader.full_name[0] }}
                        </div>
                    </div>
                    <div>
                        <small class="text-muted">
                            بواسطة <strong>{{ file.uploader.full_name }}</strong><br>
                            من {{ file.department.name }}
                        </small>
                    </div>
                </div>
                
                <div class="d-grid">
                    <a href="{{ url_for('employee.download_file', id=file.id) }}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-download me-1"></i>
                        تحميل الملف
                    </a>
                </div>
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    <i class="fas fa-calendar me-1"></i>
                    {{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                </small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if files.pages > 1 %}
<nav aria-label="صفحات الملفات" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if files.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('employee.public_files', page=files.prev_num, query=request.args.get('query', '')) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in files.iter_pages() %}
            {% if page_num %}
                {% if page_num != files.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('employee.public_files', page=page_num, query=request.args.get('query', '')) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if files.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('employee.public_files', page=files.next_num, query=request.args.get('query', '')) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- Empty State -->
<div class="text-center py-5">
    {% if request.args.get('query') %}
    <i class="fas fa-search fa-4x text-muted mb-4"></i>
    <h4 class="text-muted">لا توجد نتائج</h4>
    <p class="text-muted">لم يتم العثور على ملفات عامة تطابق البحث "{{ request.args.get('query') }}"</p>
    <a href="{{ url_for('employee.public_files') }}" class="btn btn-outline-primary">
        <i class="fas fa-times me-1"></i>
        إلغاء البحث
    </a>
    {% else %}
    <i class="fas fa-share-alt fa-4x text-muted mb-4"></i>
    <h4 class="text-muted">لا توجد ملفات عامة</h4>
    <p class="text-muted">لم يتم مشاركة أي ملفات عامة بعد</p>
    <a href="{{ url_for('employee.upload_file') }}" class="btn btn-primary">
        <i class="fas fa-cloud-upload-alt me-1"></i>
        رفع ملف ومشاركته
    </a>
    {% endif %}
</div>
{% endif %}

<!-- Info Box -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            معلومات مهمة
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-share-alt text-success me-2"></i>الملفات العامة:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>يمكن لجميع الموظفين رؤيتها</li>
                    <li><i class="fas fa-check text-success me-2"></i>يمكن تحميلها من قبل أي موظف</li>
                    <li><i class="fas fa-check text-success me-2"></i>مفيدة للمستندات المشتركة</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-lightbulb text-warning me-2"></i>نصائح:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-arrow-left text-primary me-2"></i>استخدم البحث للعثور على الملفات</li>
                    <li><i class="fas fa-arrow-left text-primary me-2"></i>تحقق من القسم والموظف المرفوع</li>
                    <li><i class="fas fa-arrow-left text-primary me-2"></i>يمكنك رفع ملفاتك كملفات عامة</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
