{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card mt-5">
            <div class="card-header text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                        {% if form.username.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.department.label(class="form-label") }}
                        {{ form.department(class="form-select" + (" is-invalid" if form.department.errors else "")) }}
                        {% if form.department.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.department.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    يرجى إدخال بيانات تسجيل الدخول الخاصة بك
                </small>
            </div>
        </div>
        
        <!-- معلومات تجريبية -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    بيانات تجريبية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <strong>المدير:</strong><br>
                        <small>
                            اسم المستخدم: admin<br>
                            كلمة المرور: admin123<br>
                            القسم: الإدارة
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    
    .card {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .navbar {
        display: none;
    }
    
    .sidebar {
        display: none;
    }
    
    main {
        width: 100% !important;
        max-width: 100% !important;
        flex: none !important;
    }
</style>
{% endblock %}
