#!/usr/bin/env python3
"""
إعداد المنافذ المتاحة لنظام إدارة الملفات
Available Ports Configuration for File Management System

هذا الملف يساعد في العثور على منفذ متاح وتشغيل النظام عليه
"""

import socket
import sys
import os

# قائمة المنافذ المفضلة للاختبار
PREFERRED_PORTS = [8080, 8000, 8888, 9000, 9080, 7000, 7080, 6000, 6080, 5555]

def is_port_available(port):
    """التحقق من توفر المنفذ"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=8080):
    """العثور على منفذ متاح"""
    # جرب المنفذ المحدد أولاً
    if is_port_available(start_port):
        return start_port
    
    # جرب المنافذ المفضلة
    for port in PREFERRED_PORTS:
        if port != start_port and is_port_available(port):
            return port
    
    # جرب منافذ عشوائية في النطاق 8000-9999
    for port in range(8000, 10000):
        if port not in PREFERRED_PORTS and is_port_available(port):
            return port
    
    return None

def update_config_files(new_port):
    """تحديث ملفات التكوين بالمنفذ الجديد"""
    files_to_update = [
        ('app.py', f'port={new_port}'),
        ('run.py', f'port={new_port}'),
        ('.env', f'PORT={new_port}')
    ]
    
    print(f"🔧 تحديث ملفات التكوين للمنفذ {new_port}...")
    
    # تحديث app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن السطر الذي يحتوي على port= وتحديثه
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'app.run(' in line and 'port=' in line:
                # استخراج المنفذ الحالي وتحديثه
                import re
                lines[i] = re.sub(r'port=\d+', f'port={new_port}', line)
                break
        
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"✅ تم تحديث app.py")
    except Exception as e:
        print(f"❌ خطأ في تحديث app.py: {e}")
    
    # تحديث run.py
    try:
        with open('run.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'port=' in line and 'app.run(' in line:
                import re
                lines[i] = re.sub(r'port=\d+', f'port={new_port}', line)
            elif 'localhost:' in line and 'الرابط:' in line:
                lines[i] = re.sub(r'localhost:\d+', f'localhost:{new_port}', line)
        
        with open('run.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"✅ تم تحديث run.py")
    except Exception as e:
        print(f"❌ خطأ في تحديث run.py: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔍 البحث عن منفذ متاح...")
    print("=" * 50)
    
    # التحقق من المنفذ الحالي
    current_port = 8080
    print(f"🔍 فحص المنفذ الحالي: {current_port}")
    
    if is_port_available(current_port):
        print(f"✅ المنفذ {current_port} متاح!")
        print(f"🌐 يمكنك تشغيل النظام على: http://localhost:{current_port}")
        return current_port
    else:
        print(f"❌ المنفذ {current_port} مستخدم")
    
    # البحث عن منفذ بديل
    print("🔍 البحث عن منفذ بديل...")
    available_port = find_available_port()
    
    if available_port:
        print(f"✅ تم العثور على منفذ متاح: {available_port}")
        
        # سؤال المستخدم عن التحديث
        response = input(f"هل تريد تحديث ملفات التكوين للمنفذ {available_port}؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم', 'ن']:
            update_config_files(available_port)
            print(f"🎉 تم تحديث النظام للمنفذ {available_port}")
            print(f"🌐 الرابط الجديد: http://localhost:{available_port}")
        else:
            print(f"💡 يمكنك تشغيل النظام يدوياً على المنفذ {available_port}")
            print(f"   python app.py --port {available_port}")
        
        return available_port
    else:
        print("❌ لم يتم العثور على منفذ متاح في النطاق المحدد")
        print("💡 جرب إغلاق بعض التطبيقات أو استخدم منفذ مخصص")
        return None

def show_port_status():
    """عرض حالة المنافذ المفضلة"""
    print("📊 حالة المنافذ المفضلة:")
    print("-" * 30)
    
    for port in PREFERRED_PORTS:
        status = "✅ متاح" if is_port_available(port) else "❌ مستخدم"
        print(f"المنفذ {port:4d}: {status}")
    
    print("-" * 30)

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--status':
        show_port_status()
    else:
        main()
