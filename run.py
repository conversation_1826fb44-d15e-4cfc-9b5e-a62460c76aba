#!/usr/bin/env python3
"""
ملف تشغيل نظام إدارة الملفات
File Management System Runner

هذا الملف يقوم بتشغيل النظام مع إعدادات مختلفة حسب البيئة
"""

import os
import sys
from app import create_app

def main():
    """تشغيل التطبيق"""
    
    # تحديد بيئة التشغيل
    config_name = os.environ.get('FLASK_CONFIG', 'development')
    
    # إنشاء التطبيق
    app = create_app(config_name)
    
    # طباعة معلومات التشغيل
    print("=" * 50)
    print("🚀 نظام إدارة الملفات")
    print("=" * 50)
    print(f"📊 البيئة: {config_name}")
    # قراءة المنفذ من متغيرات البيئة
    port = int(os.environ.get('PORT', 8080))
    host = os.environ.get('HOST', '0.0.0.0')
    print(f"🌐 الرابط: http://localhost:{port}")
    print(f"👤 المدير الافتراضي:")
    print(f"   اسم المستخدم: admin")
    print(f"   كلمة المرور: admin123")
    print(f"   القسم: الإدارة")
    print("=" * 50)
    print("💡 للإيقاف: اضغط Ctrl+C")
    print("=" * 50)
    
    try:
        # تشغيل التطبيق
        app.run(
            host=host,
            port=port,
            debug=(config_name == 'development'),
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بنجاح")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
