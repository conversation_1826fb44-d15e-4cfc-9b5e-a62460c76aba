@echo off
chcp 65001 >nul
echo ================================================
echo        🐍 تثبيت Python وتشغيل النظام
echo ================================================
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python مثبت بالفعل
    python --version
    goto install_packages
) else (
    echo ❌ Python غير مثبت
    goto install_python
)

:install_python
echo.
echo 🐍 تثبيت Python...
echo.
echo اختر طريقة التثبيت:
echo [1] فتح Microsoft Store (الأسرع)
echo [2] فتح الموقع الرسمي
echo [3] تخطي (إذا كان مثبت بالفعل)
echo.
set /p choice="اختر رقماً (1-3): "

if "%choice%"=="1" (
    echo 🌐 فتح Microsoft Store...
    start ms-windows-store://pdp/?productid=9NRWMJP3717K
    echo.
    echo ⏳ بعد انتهاء التثبيت:
    echo 1. أغلق هذه النافذة
    echo 2. افتح Command Prompt جديد
    echo 3. شغل هذا الملف مرة أخرى
    pause
    exit
)

if "%choice%"=="2" (
    echo 🌐 فتح الموقع الرسمي...
    start https://python.org/downloads/
    echo.
    echo ⚠️  تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    echo ⏳ بعد انتهاء التثبيت:
    echo 1. أغلق هذه النافذة
    echo 2. افتح Command Prompt جديد
    echo 3. شغل هذا الملف مرة أخرى
    pause
    exit
)

if "%choice%"=="3" (
    goto install_packages
)

echo خيار غير صحيح
pause
exit

:install_packages
echo.
echo 📦 تثبيت المكتبات المطلوبة...
echo.
python -m pip install --upgrade pip
python -m pip install flask flask-sqlalchemy flask-login flask-wtf wtforms werkzeug

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    echo 💡 جرب:
    echo python -m pip install --user flask flask-sqlalchemy flask-login flask-wtf wtforms werkzeug
    pause
    exit
)

echo ✅ تم تثبيت المكتبات بنجاح

:create_database
echo.
echo 🗄️  إنشاء قاعدة البيانات...
python create_db.py

if %errorlevel% neq 0 (
    echo ⚠️  تحذير: قد تكون هناك مشكلة في إنشاء قاعدة البيانات
    echo سيتم المحاولة عند تشغيل النظام
)

:run_server
echo.
echo 🚀 تشغيل النظام...
echo.
echo 🌐 الرابط: http://localhost:8080
echo 👤 المدير: admin / admin123 / الإدارة
echo.
echo ⏹️  لإيقاف النظام: اضغط Ctrl+C
echo.

python app.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل النظام
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تأكد من تثبيت Python بشكل صحيح
    echo 2. تأكد من تثبيت جميع المكتبات
    echo 3. تحقق من عدم استخدام المنفذ 8080
    echo.
    echo 📞 للمساعدة، راجع ملف PROBLEM_DIAGNOSIS_AND_SOLUTION.md
)

echo.
pause
