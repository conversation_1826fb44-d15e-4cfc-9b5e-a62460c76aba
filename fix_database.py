#!/usr/bin/env python3
"""
إصلاح مشكلة قاعدة البيانات
Fix Database Issues
"""

import os
import sys
from datetime import datetime

def create_simple_database():
    """إنشاء قاعدة بيانات مبسطة"""
    try:
        # إنشاء مجلد instance إذا لم يكن موجوداً
        if not os.path.exists('instance'):
            os.makedirs('instance')
            print("✅ تم إنشاء مجلد instance")
        
        # إنشاء ملف قاعدة البيانات
        db_path = os.path.join('instance', 'file_management.db')
        
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول الأقسام
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS department (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHA<PERSON>(100) NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) NOT NULL,
                full_name VARCHAR(120) NOT NULL,
                email VARCHAR(120),
                password_hash VARCHAR(255) NOT NULL,
                department_id INTEGER NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                FOREIGN KEY (department_id) REFERENCES department (id),
                UNIQUE (username, department_id)
            )
        ''')
        
        # إنشاء جدول الملفات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS file (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size INTEGER,
                file_type VARCHAR(50),
                description TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                uploaded_by INTEGER NOT NULL,
                department_id INTEGER NOT NULL,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (uploaded_by) REFERENCES user (id),
                FOREIGN KEY (department_id) REFERENCES department (id)
            )
        ''')
        
        # التحقق من وجود الأقسام
        cursor.execute("SELECT COUNT(*) FROM department")
        dept_count = cursor.fetchone()[0]
        
        if dept_count == 0:
            # إضافة الأقسام الافتراضية
            departments = [
                ('الإدارة', 'قسم الإدارة العامة'),
                ('الموارد البشرية', 'قسم الموارد البشرية'),
                ('المالية', 'قسم المالية والمحاسبة'),
                ('تقنية المعلومات', 'قسم تقنية المعلومات'),
                ('التسويق', 'قسم التسويق والمبيعات'),
                ('العمليات', 'قسم العمليات والإنتاج')
            ]
            
            for name, desc in departments:
                cursor.execute(
                    "INSERT INTO department (name, description) VALUES (?, ?)",
                    (name, desc)
                )
            
            print("✅ تم إضافة الأقسام الافتراضية")
        
        # التحقق من وجود المستخدم الإداري
        cursor.execute("SELECT COUNT(*) FROM user WHERE username = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            # الحصول على ID قسم الإدارة
            cursor.execute("SELECT id FROM department WHERE name = 'الإدارة'")
            admin_dept_id = cursor.fetchone()[0]
            
            # إنشاء hash لكلمة المرور باستخدام werkzeug
            try:
                from werkzeug.security import generate_password_hash
                password = 'admin123'
                password_hash = generate_password_hash(password)
            except ImportError:
                # استخدام تشفير مبسط إذا لم تكن werkzeug متاحة
                import hashlib
                password = 'admin123'
                password_hash = 'pbkdf2:sha256:260000$' + hashlib.sha256(password.encode()).hexdigest()
            
            cursor.execute('''
                INSERT INTO user (username, full_name, email, password_hash, 
                                department_id, is_admin, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', ('admin', 'مدير النظام', '<EMAIL>', 
                  password_hash, admin_dept_id, True, True))
            
            print("✅ تم إنشاء المستخدم الإداري")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
            print("   القسم: الإدارة")
        
        # إضافة مستخدم تجريبي
        cursor.execute("SELECT COUNT(*) FROM user WHERE username = 'employee1'")
        emp_count = cursor.fetchone()[0]
        
        if emp_count == 0:
            cursor.execute("SELECT id FROM department WHERE name = 'الموارد البشرية'")
            hr_dept_id = cursor.fetchone()[0]
            
            try:
                from werkzeug.security import generate_password_hash
                password = '123456'
                password_hash = generate_password_hash(password)
            except ImportError:
                import hashlib
                password = '123456'
                password_hash = 'pbkdf2:sha256:260000$' + hashlib.sha256(password.encode()).hexdigest()
            
            cursor.execute('''
                INSERT INTO user (username, full_name, email, password_hash, 
                                department_id, is_admin, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', ('employee1', 'موظف تجريبي', '<EMAIL>', 
                  password_hash, hr_dept_id, False, True))
            
            print("✅ تم إنشاء مستخدم تجريبي")
            print("   اسم المستخدم: employee1")
            print("   كلمة المرور: 123456")
            print("   القسم: الموارد البشرية")
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إنشاء قاعدة البيانات: {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔧 إصلاح قاعدة البيانات - نظام إدارة الملفات")
    print("=" * 50)
    
    if create_simple_database():
        print("\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("   1. انقر نقراً مزدوجاً على quick_start.bat")
        print("   2. أو شغل: python app.py")
        print("\n🔑 بيانات تسجيل الدخول:")
        print("   المدير: admin / admin123 / الإدارة")
        print("   موظف: employee1 / 123456 / الموارد البشرية")
    else:
        print("\n❌ فشل في إصلاح قاعدة البيانات")
    
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
