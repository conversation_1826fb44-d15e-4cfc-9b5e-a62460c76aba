{% extends "base.html" %}

{% block title %}إدارة الأقسام - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-building me-2"></i>
        إدارة الأقسام
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.add_department') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة قسم جديد
            </a>
        </div>
    </div>
</div>

{% if departments %}
<div class="row">
    {% for department in departments %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    {{ department.name }}
                </h6>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                            data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="{{ url_for('admin.edit_department', id=department.id) }}">
                                <i class="fas fa-edit me-2"></i>تعديل
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ url_for('admin.delete_department', id=department.id) }}" 
                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا القسم؟')">
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="fas fa-trash me-2"></i>حذف
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                {% if department.description %}
                <p class="card-text">{{ department.description }}</p>
                {% else %}
                <p class="card-text text-muted">لا يوجد وصف لهذا القسم</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary">{{ department.users.count() }}</h5>
                            <small class="text-muted">موظف</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success">{{ department.files.count() }}</h5>
                        <small class="text-muted">ملف</small>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    <i class="fas fa-calendar me-1"></i>
                    تم الإنشاء: {{ department.created_at.strftime('%Y-%m-%d') }}
                </small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
    <i class="fas fa-building fa-4x text-muted mb-4"></i>
    <h4 class="text-muted">لا توجد أقسام</h4>
    <p class="text-muted">ابدأ بإضافة أول قسم في النظام</p>
    <a href="{{ url_for('admin.add_department') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        إضافة قسم جديد
    </a>
</div>
{% endif %}
{% endblock %}
