{% extends "base.html" %}

{% block title %}إضافة قسم جديد - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>
        إضافة قسم جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.departments') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة إلى الأقسام
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    معلومات القسم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else ""), 
                                   placeholder="أدخل اسم القسم") }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-4">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), 
                                          rows="4", placeholder="أدخل وصف القسم (اختياري)") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">يمكنك إضافة وصف مفصل للقسم ومهامه</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.departments') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
