@echo off
echo ================================================
echo        تشغيل سريع - نظام إدارة الملفات
echo       Quick Start - File Management System
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير متاح في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

echo تم العثور على Python...
echo.

REM تشغيل السكريبت السريع
echo 🚀 بدء التشغيل السريع...
python quick_start.py

pause
