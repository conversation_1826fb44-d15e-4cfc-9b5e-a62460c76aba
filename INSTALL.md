# دليل التثبيت - نظام إدارة الملفات

## المتطلبات الأساسية

### 1. Python
- **الإصدار المطلوب:** Python 3.8 أو أحدث
- **التحميل:** [python.org](https://www.python.org/downloads/)

### 2. مساحة التخزين
- **الحد الأدنى:** 100 MB للنظام
- **إضافية:** حسب حجم الملفات المرفوعة

## طرق التثبيت

### الطريقة الأولى: التشغيل السريع

#### على Windows:
1. انقر نقراً مزدوجاً على `start.bat`
2. انتظر حتى يكتمل التثبيت
3. سيفتح النظام تلقائياً على `http://localhost:5000`

#### على Linux/Mac:
```bash
chmod +x start.sh
./start.sh
```

### الطريقة الثانية: التثبيت اليدوي

#### 1. إنشاء البيئة الافتراضية
```bash
# على Windows
python -m venv venv
venv\Scripts\activate

# على Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

#### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 3. تشغيل النظام
```bash
python app.py
```

## التحقق من التثبيت

### 1. فتح المتصفح
انتقل إلى: `http://localhost:5000`

### 2. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **القسم:** الإدارة

### 3. اختبار الوظائف
- [ ] تسجيل الدخول
- [ ] عرض لوحة التحكم
- [ ] إضافة قسم جديد
- [ ] إضافة موظف جديد
- [ ] رفع ملف تجريبي

## حل المشاكل الشائعة

### مشكلة: Python غير موجود
```
Python was not found
```
**الحل:**
1. تثبيت Python من [python.org](https://www.python.org/downloads/)
2. التأكد من إضافة Python إلى PATH
3. إعادة تشغيل Command Prompt/Terminal

### مشكلة: فشل تثبيت المتطلبات
```
ERROR: Could not install packages
```
**الحل:**
```bash
python -m pip install --upgrade pip
pip install -r requirements.txt
```

### مشكلة: خطأ في قاعدة البيانات
```
OperationalError: no such table
```
**الحل:**
1. احذف ملف `file_management.db`
2. أعد تشغيل التطبيق

### مشكلة: المنفذ مستخدم
```
Address already in use
```
**الحل:**
1. أغلق التطبيق السابق
2. أو غير المنفذ في `app.py`:
```python
app.run(port=5001)  # بدلاً من 5000
```

### مشكلة: صلاحيات الملفات (Linux/Mac)
```
Permission denied
```
**الحل:**
```bash
chmod 755 uploads/
chmod +x start.sh
```

## إعدادات متقدمة

### تغيير المنفذ
في ملف `app.py`:
```python
app.run(host='0.0.0.0', port=8080)
```

### استخدام قاعدة بيانات خارجية
في ملف `config.py`:
```python
SQLALCHEMY_DATABASE_URI = 'mysql://user:password@localhost/dbname'
```

### تفعيل HTTPS
```python
app.run(ssl_context='adhoc')
```

## النشر في الإنتاج

### 1. تغيير الإعدادات
```python
# في config.py
SECRET_KEY = 'your-production-secret-key'
DEBUG = False
```

### 2. استخدام خادم ويب
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

### 3. إعداد Nginx (اختياري)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## النسخ الاحتياطي

### نسخ احتياطي لقاعدة البيانات
```bash
cp file_management.db backup_$(date +%Y%m%d).db
```

### نسخ احتياطي للملفات
```bash
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

## التحديث

### 1. نسخ احتياطي
```bash
cp file_management.db backup.db
cp -r uploads uploads_backup
```

### 2. تحديث الكود
```bash
git pull origin main
```

### 3. تحديث المتطلبات
```bash
pip install -r requirements.txt --upgrade
```

### 4. إعادة التشغيل
```bash
python app.py
```

## الدعم

### الحصول على المساعدة
- راجع ملف `README.md`
- تحقق من الأخطاء في Terminal/Command Prompt
- ابحث في Issues على GitHub

### الإبلاغ عن مشكلة
عند الإبلاغ عن مشكلة، يرجى تضمين:
- نظام التشغيل
- إصدار Python
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة

---

**ملاحظة:** هذا النظام مصمم للاستخدام الداخلي في الشركات والمؤسسات. تأكد من اتباع سياسات الأمان المناسبة عند النشر.
