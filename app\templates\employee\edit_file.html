{% extends "base.html" %}

{% block title %}تعديل الملف - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>
        تعديل الملف: {{ file.title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('employee.my_files') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة إلى ملفاتي
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-edit me-2"></i>
                    تعديل معلومات الملف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <!-- File Title -->
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- File Description -->
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), 
                                          rows="4") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">يمكنك إضافة وصف مفصل للملف لتسهيل البحث عنه لاحقاً</div>
                    </div>
                    
                    <!-- Public File Option -->
                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_public(class="form-check-input") }}
                            {{ form.is_public.label(class="form-check-label") }}
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            إذا تم تفعيل هذا الخيار، سيتمكن جميع الموظفين من رؤية وتحميل هذا الملف
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('employee.my_files') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        
        <!-- File Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الملف
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>اسم الملف الأصلي:</strong></td>
                                <td>{{ file.original_filename }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع الملف:</strong></td>
                                <td>
                                    <span class="badge bg-info">{{ file.file_type }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حجم الملف:</strong></td>
                                <td>{{ file.get_file_size_formatted() }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>تاريخ الرفع:</strong></td>
                                <td>{{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد التحميلات:</strong></td>
                                <td>{{ file.download_count }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع الوصول:</strong></td>
                                <td>
                                    {% if file.is_public %}
                                    <span class="badge bg-success">عام</span>
                                    {% else %}
                                    <span class="badge bg-warning">خاص</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="{{ url_for('employee.download_file', id=file.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-download me-1"></i>
                        تحميل الملف
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
