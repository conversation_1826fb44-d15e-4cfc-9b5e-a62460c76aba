#!/usr/bin/env python3
"""
فحص حالة النظام
System Status Check
"""

import os
import sys

def check_files():
    """فحص الملفات المطلوبة"""
    print("📁 فحص الملفات...")
    
    required_files = [
        'app.py',
        'requirements.txt',
        'config.py',
        'app/__init__.py',
        'app/models.py',
        'app/auth/routes.py',
        'app/templates/auth/login.html',
        'app/templates/base.html'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    return len(missing_files) == 0

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️  فحص قاعدة البيانات...")
    
    db_path = os.path.join('instance', 'file_management.db')
    
    if not os.path.exists('instance'):
        print("❌ مجلد instance غير موجود")
        return False
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    print(f"✅ قاعدة البيانات موجودة: {db_path}")
    
    # فحص محتوى قاعدة البيانات
    try:
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📊 الجداول الموجودة: {len(tables)}")
        for table in tables:
            print(f"   - {table[0]}")
        
        # فحص الأقسام
        try:
            cursor.execute("SELECT COUNT(*) FROM department")
            dept_count = cursor.fetchone()[0]
            print(f"📁 عدد الأقسام: {dept_count}")
        except:
            print("❌ جدول الأقسام غير موجود أو فارغ")
        
        # فحص المستخدمين
        try:
            cursor.execute("SELECT COUNT(*) FROM user")
            user_count = cursor.fetchone()[0]
            print(f"👤 عدد المستخدمين: {user_count}")
        except:
            print("❌ جدول المستخدمين غير موجود أو فارغ")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_python_modules():
    """فحص المكتبات المطلوبة"""
    print("\n🐍 فحص مكتبات Python...")
    
    required_modules = [
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'flask_wtf',
        'wtforms',
        'werkzeug'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    return len(missing_modules) == 0

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔍 فحص حالة نظام إدارة الملفات")
    print("=" * 50)
    
    all_good = True
    
    # فحص الملفات
    if not check_files():
        all_good = False
        print("\n💡 بعض الملفات مفقودة")
    
    # فحص المكتبات
    if not check_python_modules():
        all_good = False
        print("\n💡 بعض المكتبات مفقودة")
        print("   شغل: pip install -r requirements.txt")
    
    # فحص قاعدة البيانات
    if not check_database():
        all_good = False
        print("\n💡 قاعدة البيانات تحتاج إصلاح")
        print("   شغل: python fix_database.py")
    
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 النظام جاهز للتشغيل!")
        print("🚀 شغل: python app.py")
    else:
        print("⚠️  النظام يحتاج إصلاح")
        print("🔧 شغل: python fix_database.py")
    print("=" * 50)
    
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
