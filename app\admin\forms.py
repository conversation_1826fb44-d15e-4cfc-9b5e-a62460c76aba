from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, BooleanField, PasswordField, SubmitField
from wtforms.validators import DataRequired, Length, Email, Optional, EqualTo
from app.models import Department, User

class DepartmentForm(FlaskForm):
    """نموذج إضافة/تعديل قسم"""
    name = StringField('اسم القسم', validators=[
        DataRequired(message='اسم القسم مطلوب'),
        Length(min=2, max=100, message='اسم القسم يجب أن يكون بين 2 و 100 حرف')
    ])
    description = TextAreaField('وصف القسم', validators=[
        Optional(),
        Length(max=500, message='الوصف يجب ألا يزيد عن 500 حرف')
    ])
    submit = SubmitField('حفظ')

class UserForm(FlaskForm):
    """نموذج إضافة/تعديل موظف"""
    username = <PERSON>F<PERSON>('اسم المستخدم', validators=[
        DataRequired(message='اسم المستخدم مطلوب'),
        Length(min=3, max=80, message='اسم المستخدم يجب أن يكون بين 3 و 80 حرف')
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(message='البريد الإلكتروني مطلوب'),
        Email(message='البريد الإلكتروني غير صحيح'),
        Length(max=120, message='البريد الإلكتروني طويل جداً')
    ])
    full_name = StringField('الاسم الكامل', validators=[
        DataRequired(message='الاسم الكامل مطلوب'),
        Length(min=2, max=200, message='الاسم الكامل يجب أن يكون بين 2 و 200 حرف')
    ])
    password = PasswordField('كلمة المرور', validators=[
        DataRequired(message='كلمة المرور مطلوبة'),
        Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    ])
    password2 = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(message='تأكيد كلمة المرور مطلوب'),
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    department_id = SelectField('القسم', coerce=int, validators=[
        DataRequired(message='يرجى اختيار القسم')
    ])
    is_admin = BooleanField('مدير النظام')
    is_active = BooleanField('نشط', default=True)
    submit = SubmitField('حفظ')
    
    def __init__(self, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        self.department_id.choices = [
            (dept.id, dept.name) for dept in Department.query.all()
        ]

class EditUserForm(FlaskForm):
    """نموذج تعديل موظف (بدون كلمة مرور إجبارية)"""
    username = StringField('اسم المستخدم', validators=[
        DataRequired(message='اسم المستخدم مطلوب'),
        Length(min=3, max=80, message='اسم المستخدم يجب أن يكون بين 3 و 80 حرف')
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(message='البريد الإلكتروني مطلوب'),
        Email(message='البريد الإلكتروني غير صحيح'),
        Length(max=120, message='البريد الإلكتروني طويل جداً')
    ])
    full_name = StringField('الاسم الكامل', validators=[
        DataRequired(message='الاسم الكامل مطلوب'),
        Length(min=2, max=200, message='الاسم الكامل يجب أن يكون بين 2 و 200 حرف')
    ])
    password = PasswordField('كلمة المرور الجديدة (اتركها فارغة إذا لم ترد تغييرها)', validators=[
        Optional(),
        Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    ])
    password2 = PasswordField('تأكيد كلمة المرور الجديدة', validators=[
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    department_id = SelectField('القسم', coerce=int, validators=[
        DataRequired(message='يرجى اختيار القسم')
    ])
    is_admin = BooleanField('مدير النظام')
    is_active = BooleanField('نشط')
    submit = SubmitField('حفظ التغييرات')
    
    def __init__(self, *args, **kwargs):
        super(EditUserForm, self).__init__(*args, **kwargs)
        self.department_id.choices = [
            (dept.id, dept.name) for dept in Department.query.all()
        ]

class SearchForm(FlaskForm):
    """نموذج البحث"""
    query = StringField('البحث', validators=[Optional()])
    department_id = SelectField('القسم', coerce=int, validators=[Optional()])
    file_type = SelectField('نوع الملف', validators=[Optional()])
    submit = SubmitField('بحث')
    
    def __init__(self, *args, **kwargs):
        super(SearchForm, self).__init__(*args, **kwargs)
        self.department_id.choices = [(0, 'جميع الأقسام')] + [
            (dept.id, dept.name) for dept in Department.query.all()
        ]
        self.file_type.choices = [
            ('', 'جميع الأنواع'),
            ('image', 'صور'),
            ('document', 'مستندات'),
            ('spreadsheet', 'جداول بيانات'),
            ('presentation', 'عروض تقديمية'),
            ('archive', 'ملفات مضغوطة'),
            ('other', 'أخرى')
        ]
