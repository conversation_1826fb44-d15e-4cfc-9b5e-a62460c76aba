from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, BooleanField, PasswordField, SubmitField
from wtforms.validators import DataRequired, Length, Email, Optional, EqualTo
from app.models import Department, User

class DepartmentForm(FlaskForm):
    """نموذج إضافة/تعديل قسم"""
    name = StringField('اسم القسم', validators=[
        DataRequired(message='اسم القسم مطلوب'),
        Length(min=2, max=100, message='اسم القسم يجب أن يكون بين 2 و 100 حرف')
    ])
    description = TextAreaField('وصف القسم', validators=[
        Optional(),
        Length(max=500, message='الوصف يجب ألا يزيد عن 500 حرف')
    ])
    submit = SubmitField('حفظ')

class UserForm(FlaskForm):
    """نموذج إضافة/تعديل موظف"""
    username = <PERSON>F<PERSON>('اسم المستخدم', validators=[
        DataRequired(message='اسم المستخدم مطلوب'),
        Length(min=3, max=80, message='اسم المستخدم يجب أن يكون بين 3 و 80 حرف')
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(message='البريد الإلكتروني مطلوب'),
        Email(message='البريد الإلكتروني غير صحيح'),
        Length(max=120, message='البريد الإلكتروني طويل جداً')
    ])
    full_name = StringField('الاسم الكامل', validators=[
        DataRequired(message='الاسم الكامل مطلوب'),
        Length(min=2, max=200, message='الاسم الكامل يجب أن يكون بين 2 و 200 حرف')
    ])
    password = PasswordField('كلمة المرور', validators=[
        DataRequired(message='كلمة المرور مطلوبة'),
        Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    ])
    password2 = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(message='تأكيد كلمة المرور مطلوب'),
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    department_id = SelectField('القسم', coerce=int, validators=[
        DataRequired(message='يرجى اختيار القسم')
    ])
    is_admin = BooleanField('مدير النظام')
    is_active = BooleanField('نشط', default=True)
    submit = SubmitField('حفظ')
    
    def __init__(self, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        self.department_id.choices = [
            (dept.id, dept.name) for dept in Department.query.all()
        ]

class EditUserForm(FlaskForm):
    """نموذج تعديل موظف (بدون كلمة مرور إجبارية)"""
    username = StringField('اسم المستخدم', validators=[
        DataRequired(message='اسم المستخدم مطلوب'),
        Length(min=3, max=80, message='اسم المستخدم يجب أن يكون بين 3 و 80 حرف')
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(message='البريد الإلكتروني مطلوب'),
        Email(message='البريد الإلكتروني غير صحيح'),
        Length(max=120, message='البريد الإلكتروني طويل جداً')
    ])
    full_name = StringField('الاسم الكامل', validators=[
        DataRequired(message='الاسم الكامل مطلوب'),
        Length(min=2, max=200, message='الاسم الكامل يجب أن يكون بين 2 و 200 حرف')
    ])
    password = PasswordField('كلمة المرور الجديدة (اتركها فارغة إذا لم ترد تغييرها)', validators=[
        Optional(),
        Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    ])
    password2 = PasswordField('تأكيد كلمة المرور الجديدة', validators=[
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    department_id = SelectField('القسم', coerce=int, validators=[
        DataRequired(message='يرجى اختيار القسم')
    ])
    is_admin = BooleanField('مدير النظام')
    is_active = BooleanField('نشط')
    submit = SubmitField('حفظ التغييرات')
    
    def __init__(self, *args, **kwargs):
        super(EditUserForm, self).__init__(*args, **kwargs)
        self.department_id.choices = [
            (dept.id, dept.name) for dept in Department.query.all()
        ]

class SearchForm(FlaskForm):
    """نموذج البحث المتقدم"""
    query = StringField('البحث', validators=[Optional()],
                       render_kw={"placeholder": "البحث في العنوان، الوصف، اسم الملف، الموظف..."})
    department_id = SelectField('القسم', coerce=int, validators=[Optional()])
    file_type = SelectField('نوع الملف', validators=[Optional()])
    uploader_id = SelectField('الموظف', coerce=int, validators=[Optional()])
    is_public = SelectField('نوع الوصول', validators=[Optional()])
    date_from = StringField('من تاريخ', validators=[Optional()],
                           render_kw={"type": "date"})
    date_to = StringField('إلى تاريخ', validators=[Optional()],
                         render_kw={"type": "date"})
    sort_by = SelectField('ترتيب حسب', validators=[Optional()])
    sort_order = SelectField('نوع الترتيب', validators=[Optional()])
    submit = SubmitField('بحث')
    clear = SubmitField('مسح الفلاتر')

    def __init__(self, *args, **kwargs):
        super(SearchForm, self).__init__(*args, **kwargs)

        # خيارات الأقسام
        self.department_id.choices = [(0, 'جميع الأقسام')] + [
            (dept.id, dept.name) for dept in Department.query.all()
        ]

        # خيارات أنواع الملفات
        self.file_type.choices = [
            ('', 'جميع الأنواع'),
            ('image', 'صور'),
            ('document', 'مستندات'),
            ('spreadsheet', 'جداول بيانات'),
            ('presentation', 'عروض تقديمية'),
            ('archive', 'ملفات مضغوطة'),
            ('other', 'أخرى')
        ]

        # خيارات الموظفين
        self.uploader_id.choices = [(0, 'جميع الموظفين')] + [
            (user.id, user.full_name) for user in User.query.filter_by(is_active=True).all()
        ]

        # خيارات نوع الوصول
        self.is_public.choices = [
            ('', 'جميع الملفات'),
            ('1', 'الملفات العامة'),
            ('0', 'الملفات الخاصة')
        ]

        # خيارات الترتيب
        self.sort_by.choices = [
            ('uploaded_at', 'تاريخ الرفع'),
            ('title', 'العنوان'),
            ('size', 'الحجم'),
            ('uploader', 'الموظف'),
            ('department', 'القسم')
        ]

        self.sort_order.choices = [
            ('desc', 'تنازلي'),
            ('asc', 'تصاعدي')
        ]
