@echo off
chcp 65001 >nul
echo ================================================
echo        🚀 نظام إدارة الملفات - التشغيل السريع
echo ================================================
echo.

echo 📋 الخيارات المتاحة:
echo.
echo [1] عرض صفحة تسجيل الدخول التجريبية (يعمل فوراً)
echo [2] تشغيل النظام الكامل (يحتاج Python)
echo [3] إصلاح قاعدة البيانات
echo [4] فحص حالة النظام
echo [5] عرض التعليمات
echo [0] خروج
echo.

set /p choice="اختر رقماً (1-5): "

if "%choice%"=="1" goto show_demo
if "%choice%"=="2" goto run_full
if "%choice%"=="3" goto fix_db
if "%choice%"=="4" goto check_system
if "%choice%"=="5" goto show_help
if "%choice%"=="0" goto exit
goto invalid

:show_demo
echo.
echo 🌐 فتح صفحة تسجيل الدخول التجريبية...
start "" "test_login.html"
echo ✅ تم فتح الصفحة في المتصفح
echo.
echo 💡 هذه صفحة تجريبية تعرض شكل النظام
echo    للتشغيل الكامل، اختر الخيار رقم 2
goto menu

:run_full
echo.
echo 🚀 تشغيل النظام الكامل...
echo 🔧 إنشاء قاعدة البيانات...
python create_db.py
echo.
echo 🌐 تشغيل الخادم...
echo    الرابط: http://localhost:8080
echo    المستخدم: admin
echo    كلمة المرور: admin123
echo    القسم: الإدارة
echo.
python app.py
goto end

:fix_db
echo.
echo 🔧 إصلاح قاعدة البيانات...
python fix_database.py
pause
goto menu

:check_system
echo.
echo 🔍 فحص حالة النظام...
python check_system.py
pause
goto menu

:show_help
echo.
echo 📖 تعليمات الاستخدام:
echo.
echo 1️⃣  صفحة تجريبية:
echo    - تعرض شكل النظام فقط
echo    - تعمل بدون Python
echo    - للمعاينة السريعة
echo.
echo 2️⃣  النظام الكامل:
echo    - يحتاج Python 3.8+
echo    - يحتاج تثبيت المكتبات
echo    - يعمل على http://localhost:8080
echo.
echo 3️⃣  متطلبات التشغيل:
echo    - Python 3.8 أو أحدث
echo    - pip install -r requirements.txt
echo    - مساحة 50MB على القرص
echo.
echo 4️⃣  بيانات تسجيل الدخول:
echo    - المدير: admin / admin123 / الإدارة
echo    - موظف: employee1 / 123456 / الموارد البشرية
echo.
pause
goto menu

:invalid
echo.
echo ❌ خيار غير صحيح، يرجى اختيار رقم من 1 إلى 5
echo.
goto menu

:menu
echo.
echo ================================
set /p choice="اختر خياراً آخر (1-5) أو 0 للخروج: "

if "%choice%"=="1" goto show_demo
if "%choice%"=="2" goto run_full
if "%choice%"=="3" goto fix_db
if "%choice%"=="4" goto check_system
if "%choice%"=="5" goto show_help
if "%choice%"=="0" goto exit
goto invalid

:exit
echo.
echo 👋 شكراً لاستخدام نظام إدارة الملفات
echo.

:end
pause
