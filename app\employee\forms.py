from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Optional
from config import Config

class FileUploadForm(FlaskForm):
    """نموذج رفع الملفات"""
    title = StringField('عنوان الملف', validators=[
        DataRequired(message='عنوان الملف مطلوب'),
        Length(min=2, max=200, message='عنوان الملف يجب أن يكون بين 2 و 200 حرف')
    ])
    description = TextAreaField('وصف الملف', validators=[
        Optional(),
        Length(max=1000, message='الوصف يجب ألا يزيد عن 1000 حرف')
    ])
    file = FileField('اختر الملف', validators=[
        FileRequired(message='يرجى اختيار ملف'),
        FileAllowed(Config.ALLOWED_EXTENSIONS, 
                   message=f'أنواع الملفات المسموحة: {", ".join(Config.ALLOWED_EXTENSIONS)}')
    ])
    is_public = BooleanField('ملف عام (يمكن لجميع الموظفين رؤيته)')
    submit = SubmitField('رفع الملف')

class EditFileForm(FlaskForm):
    """نموذج تعديل معلومات الملف"""
    title = StringField('عنوان الملف', validators=[
        DataRequired(message='عنوان الملف مطلوب'),
        Length(min=2, max=200, message='عنوان الملف يجب أن يكون بين 2 و 200 حرف')
    ])
    description = TextAreaField('وصف الملف', validators=[
        Optional(),
        Length(max=1000, message='الوصف يجب ألا يزيد عن 1000 حرف')
    ])
    is_public = BooleanField('ملف عام (يمكن لجميع الموظفين رؤيته)')
    submit = SubmitField('حفظ التغييرات')

class SearchFileForm(FlaskForm):
    """نموذج البحث في الملفات"""
    query = StringField('البحث في الملفات', validators=[Optional()])
    submit = SubmitField('بحث')
