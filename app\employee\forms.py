from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Optional
from config import Config

class FileUploadForm(FlaskForm):
    """نموذج رفع الملفات"""
    title = StringField('عنوان الملف', validators=[
        DataRequired(message='عنوان الملف مطلوب'),
        Length(min=2, max=200, message='عنوان الملف يجب أن يكون بين 2 و 200 حرف')
    ])
    description = TextAreaField('وصف الملف', validators=[
        Optional(),
        Length(max=1000, message='الوصف يجب ألا يزيد عن 1000 حرف')
    ])
    file = FileField('اختر الملف', validators=[
        FileRequired(message='يرجى اختيار ملف'),
        FileAllowed(Config.ALLOWED_EXTENSIONS, 
                   message=f'أنواع الملفات المسموحة: {", ".join(Config.ALLOWED_EXTENSIONS)}')
    ])
    is_public = BooleanField('ملف عام (يمكن لجميع الموظفين رؤيته)')
    submit = SubmitField('رفع الملف')

class EditFileForm(FlaskForm):
    """نموذج تعديل معلومات الملف"""
    title = StringField('عنوان الملف', validators=[
        DataRequired(message='عنوان الملف مطلوب'),
        Length(min=2, max=200, message='عنوان الملف يجب أن يكون بين 2 و 200 حرف')
    ])
    description = TextAreaField('وصف الملف', validators=[
        Optional(),
        Length(max=1000, message='الوصف يجب ألا يزيد عن 1000 حرف')
    ])
    is_public = BooleanField('ملف عام (يمكن لجميع الموظفين رؤيته)')
    submit = SubmitField('حفظ التغييرات')

class SearchFileForm(FlaskForm):
    """نموذج البحث في الملفات"""
    query = StringField('البحث في الملفات', validators=[Optional()],
                       render_kw={"placeholder": "البحث في العنوان، الوصف، اسم الملف..."})
    file_type = SelectField('نوع الملف', validators=[Optional()])
    is_public = SelectField('نوع الوصول', validators=[Optional()])
    sort_by = SelectField('ترتيب حسب', validators=[Optional()])
    sort_order = SelectField('نوع الترتيب', validators=[Optional()])
    submit = SubmitField('بحث')

    def __init__(self, *args, **kwargs):
        super(SearchFileForm, self).__init__(*args, **kwargs)

        # خيارات أنواع الملفات
        self.file_type.choices = [
            ('', 'جميع الأنواع'),
            ('image', 'صور'),
            ('document', 'مستندات'),
            ('spreadsheet', 'جداول بيانات'),
            ('presentation', 'عروض تقديمية'),
            ('archive', 'ملفات مضغوطة'),
            ('other', 'أخرى')
        ]

        # خيارات نوع الوصول
        self.is_public.choices = [
            ('', 'جميع الملفات'),
            ('1', 'الملفات العامة'),
            ('0', 'الملفات الخاصة')
        ]

        # خيارات الترتيب
        self.sort_by.choices = [
            ('uploaded_at', 'تاريخ الرفع'),
            ('title', 'العنوان'),
            ('size', 'الحجم'),
            ('downloads', 'عدد التحميلات')
        ]

        self.sort_order.choices = [
            ('desc', 'تنازلي'),
            ('asc', 'تصاعدي')
        ]
