{% extends "base.html" %}

{% block title %}لوحة تحكم المدير - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة تحكم المدير
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير التقرير
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي الموظفين
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.total_users }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-2 h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            الأقسام
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.total_departments }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-3 h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي الملفات
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.total_files }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-4 h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            الموظفين النشطين
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.active_users }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Files and Users -->
<div class="row">
    <!-- Recent Files -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-file-alt me-2"></i>
                    أحدث الملفات المرفوعة
                </h6>
                <a href="{{ url_for('admin.files') }}" class="btn btn-sm btn-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_files %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الملف</th>
                                <th>الموظف</th>
                                <th>القسم</th>
                                <th>تاريخ الرفع</th>
                                <th>الحجم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file in recent_files %}
                            <tr>
                                <td>
                                    <i class="{{ file.file_type | get_file_icon }} file-icon text-primary"></i>
                                    <strong>{{ file.title }}</strong><br>
                                    <small class="text-muted">{{ file.original_filename }}</small>
                                </td>
                                <td>{{ file.uploader.full_name }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ file.department.name }}</span>
                                </td>
                                <td>
                                    <small>{{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    <small>{{ file.get_file_size_formatted() }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد ملفات مرفوعة بعد</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Users -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-user-plus me-2"></i>
                    أحدث الموظفين
                </h6>
                <a href="{{ url_for('admin.users') }}" class="btn btn-sm btn-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_users %}
                {% for user in recent_users %}
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar me-3">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                             style="width: 40px; height: 40px;">
                            {{ user.full_name[0] }}
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0">{{ user.full_name }}</h6>
                        <small class="text-muted">{{ user.department.name }}</small>
                        {% if user.is_admin %}
                        <span class="badge bg-warning ms-2">مدير</span>
                        {% endif %}
                    </div>
                    <div class="text-end">
                        <small class="text-muted">
                            {{ user.created_at.strftime('%m-%d') }}
                        </small>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users fa-2x text-muted mb-3"></i>
                    <p class="text-muted">لا يوجد موظفين جدد</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.add_user') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus fa-2x mb-2"></i><br>
                            إضافة موظف جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.add_department') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-building fa-2x mb-2"></i><br>
                            إضافة قسم جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.files') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-search fa-2x mb-2"></i><br>
                            البحث في الملفات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-users-cog fa-2x mb-2"></i><br>
                            إدارة الموظفين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إضافة فلتر للأيقونات
document.addEventListener('DOMContentLoaded', function() {
    // يمكن إضافة JavaScript إضافي هنا
});
</script>
{% endblock %}
