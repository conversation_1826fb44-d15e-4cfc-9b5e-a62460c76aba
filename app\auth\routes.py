from flask import render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user
from app.auth import bp
from app.auth.forms import LoginForm
from app.models import User
from app import db

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if current_user.is_authenticated:
        if current_user.is_admin:
            return redirect(url_for('admin.dashboard'))
        else:
            return redirect(url_for('employee.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        # البحث عن المستخدم
        user = User.query.filter_by(
            username=form.username.data,
            department_id=form.department.data
        ).first()
        
        if user and user.check_password(form.password.data) and user.is_active:
            # تسجيل الدخول
            login_user(user, remember=True)
            user.update_last_login()
            
            # تحديد الصفحة التالية
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                if user.is_admin:
                    next_page = url_for('admin.dashboard')
                else:
                    next_page = url_for('employee.dashboard')
            
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور أو القسم غير صحيح', 'error')
    
    return render_template('auth/login.html', form=form)

@bp.route('/logout')
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('auth.login'))

# User loader للـ Flask-Login
from app import login_manager

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))
