# 🚀 دليل التشغيل السريع

## طرق التشغيل السريع

### 1. التشغيل التلقائي (الأسهل) ⚡

#### على Windows:
```bash
# انقر نقراً مزدوجاً على أي من الملفات التالية:
quick_start.bat    # تشغيل سريع مع اختيار منفذ تلقائي
start.bat          # تشغيل عادي
```

#### على Linux/Mac:
```bash
chmod +x quick_start.sh
./quick_start.sh
```

### 2. التشغيل عبر Python 🐍

```bash
# التشغيل السريع مع اختيار منفذ تلقائي
python quick_start.py

# التشغيل العادي
python app.py

# التشغيل المتقدم
python run.py
```

### 3. فحص المنافذ المتاحة 🔍

```bash
# عرض حالة المنافذ
python config_ports.py --status

# البحث عن منفذ متاح وتحديث الإعدادات
python config_ports.py
```

## حل مشكلة المنفذ المستخدم 🔧

إذا ظهرت رسالة خطأ مثل:
```
Address already in use
Port 8080 is already in use
```

### الحل الأول: التشغيل السريع
```bash
python quick_start.py
```
سيختار منفذاً متاحاً تلقائياً.

### الحل الثاني: تحديد منفذ مخصص
```bash
# تحرير ملف .env
PORT=9000

# أو استخدام متغير البيئة
set PORT=9000        # Windows
export PORT=9000     # Linux/Mac
python app.py
```

### الحل الثالث: إيقاف التطبيق المستخدم للمنفذ
```bash
# Windows
netstat -ano | findstr :8080
taskkill /PID [رقم_العملية] /F

# Linux/Mac
lsof -ti:8080 | xargs kill -9
```

## المنافذ المفضلة 📊

النظام يجرب المنافذ التالية بالترتيب:
- 8080 (افتراضي)
- 8000
- 8888
- 9000
- 9080
- 7000
- 7080
- 6000
- 6080
- 5555

## بيانات تسجيل الدخول 🔑

بعد تشغيل النظام:
- **الرابط:** http://localhost:[المنفذ]
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **القسم:** الإدارة

## استكشاف الأخطاء 🔍

### خطأ: Python غير موجود
```bash
# تثبيت Python من python.org
# التأكد من إضافة Python إلى PATH
```

### خطأ: فشل تثبيت المتطلبات
```bash
python -m pip install --upgrade pip
pip install -r requirements.txt
```

### خطأ: لا يمكن الوصول للموقع
```bash
# تحقق من:
# 1. تشغيل النظام بنجاح
# 2. المنفذ الصحيح
# 3. عدم حجب Firewall
```

### خطأ: قاعدة البيانات
```bash
# احذف ملف قاعدة البيانات وأعد التشغيل
del file_management.db     # Windows
rm file_management.db      # Linux/Mac
python app.py
```

## نصائح للاستخدام الأمثل 💡

1. **استخدم التشغيل السريع** للمرة الأولى
2. **احفظ الرابط** في المفضلة
3. **أنشئ أقسام** قبل إضافة الموظفين
4. **جرب رفع ملف تجريبي** للتأكد من عمل النظام
5. **راجع لوحة التحكم** لمتابعة الإحصائيات

## الدعم والمساعدة 🆘

- راجع `README.md` للتوثيق الكامل
- راجع `INSTALL.md` لتعليمات التثبيت المفصلة
- راجع `CHANGELOG.md` لمعرفة المميزات الجديدة

---

**تم إنشاء هذا النظام بعناية لسهولة الاستخدام. إذا واجهت أي مشاكل، تأكد من اتباع الخطوات بالترتيب.**
