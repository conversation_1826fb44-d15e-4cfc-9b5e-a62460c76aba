<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الملفات - نسخة تجريبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .dashboard-container {
            min-height: 100vh;
            background: #f8f9fa;
        }
        .card {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
            border-radius: 1rem;
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 56px);
            box-shadow: 2px 0 4px rgba(0,0,0,.1);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stats-card-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .stats-card-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .stats-card-4 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        .hidden { display: none !important; }
    </style>
</head>
<body>
    <!-- صفحة تسجيل الدخول -->
    <div id="loginPage" class="login-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <!-- تنبيه حالة النظام -->
                    <div class="alert alert-warning text-center mb-4">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>تنبيه مهم</h5>
                        <p class="mb-2">Python غير مثبت على النظام</p>
                        <small>هذه نسخة تجريبية تعمل بدون خادم</small>
                    </div>

                    <div class="card">
                        <div class="card-header text-center bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </h4>
                        </div>
                        <div class="card-body">
                            <div id="loginAlert" class="alert alert-danger hidden"></div>
                            
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>اسم المستخدم
                                    </label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>كلمة المرور
                                    </label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="department" class="form-label">
                                        <i class="fas fa-building me-2"></i>القسم
                                    </label>
                                    <select class="form-select" id="department" required>
                                        <option value="">اختر القسم</option>
                                        <option value="admin">الإدارة</option>
                                        <option value="hr">الموارد البشرية</option>
                                        <option value="finance">المالية</option>
                                        <option value="it">تقنية المعلومات</option>
                                    </select>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        تسجيل الدخول
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- بيانات تجريبية -->
                    <div class="card mt-3">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-key me-2"></i>
                                بيانات تجريبية
                            </h6>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="fillAdmin()">
                                <i class="fas fa-user-tie me-2"></i>
                                المدير: admin / admin123 / الإدارة
                            </button>
                            <button class="btn btn-outline-info btn-sm w-100" onclick="fillEmployee()">
                                <i class="fas fa-user me-2"></i>
                                موظف: employee / 123456 / الموارد البشرية
                            </button>
                        </div>
                    </div>

                    <!-- تعليمات تثبيت Python -->
                    <div class="card mt-3">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-download me-2"></i>
                                لتشغيل النظام الكامل
                            </h6>
                        </div>
                        <div class="card-body">
                            <ol class="mb-0">
                                <li>ثبت Python من <a href="https://www.python.org/downloads/" target="_blank">python.org</a></li>
                                <li>شغل: <code>pip install flask flask-sqlalchemy flask-login flask-wtf</code></li>
                                <li>شغل: <code>python app.py</code></li>
                                <li>افتح: <code>http://localhost:8080</code></li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- لوحة التحكم -->
    <div id="dashboardPage" class="dashboard-container hidden">
        <!-- شريط التنقل -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-folder-open me-2"></i>
                    نظام إدارة الملفات
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <span id="currentUser">مدير النظام</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid">
            <div class="row">
                <!-- الشريط الجانبي -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    لوحة التحكم
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showSection('departments')">
                                    <i class="fas fa-building me-2"></i>
                                    إدارة الأقسام
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showSection('users')">
                                    <i class="fas fa-users me-2"></i>
                                    إدارة الموظفين
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showSection('files')">
                                    <i class="fas fa-folder-open me-2"></i>
                                    جميع الملفات
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- المحتوى الرئيسي -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <div class="pt-3">
                        <!-- لوحة التحكم الرئيسية -->
                        <div id="dashboardSection">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
                            </div>

                            <!-- الإحصائيات -->
                            <div class="row mb-4">
                                <div class="col-xl-3 col-md-6 mb-4">
                                    <div class="card stats-card">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col">
                                                    <h5 class="card-title">إجمالي الملفات</h5>
                                                    <h2 class="mb-0">156</h2>
                                                </div>
                                                <div class="col-auto">
                                                    <i class="fas fa-file fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6 mb-4">
                                    <div class="card stats-card-2">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col">
                                                    <h5 class="card-title">الموظفين</h5>
                                                    <h2 class="mb-0">24</h2>
                                                </div>
                                                <div class="col-auto">
                                                    <i class="fas fa-users fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6 mb-4">
                                    <div class="card stats-card-3">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col">
                                                    <h5 class="card-title">الأقسام</h5>
                                                    <h2 class="mb-0">6</h2>
                                                </div>
                                                <div class="col-auto">
                                                    <i class="fas fa-building fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6 mb-4">
                                    <div class="card stats-card-4">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col">
                                                    <h5 class="card-title">المساحة المستخدمة</h5>
                                                    <h2 class="mb-0">2.4 GB</h2>
                                                </div>
                                                <div class="col-auto">
                                                    <i class="fas fa-hdd fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- رسالة النجاح -->
                            <div class="alert alert-success">
                                <h4><i class="fas fa-check-circle me-2"></i>تم تسجيل الدخول بنجاح!</h4>
                                <p class="mb-2">مرحباً بك في نظام إدارة الملفات</p>
                                <hr>
                                <p class="mb-0">
                                    <strong>ملاحظة:</strong> هذه نسخة تجريبية تعمل بدون خادم. 
                                    لتشغيل النظام الكامل، يرجى تثبيت Python واتباع التعليمات.
                                </p>
                            </div>
                        </div>

                        <!-- أقسام أخرى (مخفية) -->
                        <div id="departmentsSection" class="hidden">
                            <h2><i class="fas fa-building me-2"></i>إدارة الأقسام</h2>
                            <div class="alert alert-info">
                                هذا القسم متاح في النسخة الكاملة بعد تثبيت Python
                            </div>
                        </div>

                        <div id="usersSection" class="hidden">
                            <h2><i class="fas fa-users me-2"></i>إدارة الموظفين</h2>
                            <div class="alert alert-info">
                                هذا القسم متاح في النسخة الكاملة بعد تثبيت Python
                            </div>
                        </div>

                        <div id="filesSection" class="hidden">
                            <h2><i class="fas fa-folder-open me-2"></i>جميع الملفات</h2>
                            <div class="alert alert-info">
                                هذا القسم متاح في النسخة الكاملة بعد تثبيت Python
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const department = document.getElementById('department').value;
            
            const alertDiv = document.getElementById('loginAlert');
            
            if (!username || !password || !department) {
                showAlert('يرجى ملء جميع الحقول', 'danger');
                return;
            }
            
            // التحقق من البيانات
            if ((username === 'admin' && password === 'admin123' && department === 'admin') ||
                (username === 'employee' && password === '123456' && department === 'hr')) {
                
                // تسجيل دخول ناجح
                document.getElementById('loginPage').classList.add('hidden');
                document.getElementById('dashboardPage').classList.remove('hidden');
                
                // تحديث اسم المستخدم
                document.getElementById('currentUser').textContent = 
                    username === 'admin' ? 'مدير النظام' : 'موظف تجريبي';
                
            } else {
                showAlert('بيانات تسجيل الدخول غير صحيحة', 'danger');
            }
        });

        // عرض التنبيهات
        function showAlert(message, type) {
            const alertDiv = document.getElementById('loginAlert');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.classList.remove('hidden');
            
            setTimeout(() => {
                alertDiv.classList.add('hidden');
            }, 5000);
        }

        // ملء بيانات المدير
        function fillAdmin() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            document.getElementById('department').value = 'admin';
        }

        // ملء بيانات الموظف
        function fillEmployee() {
            document.getElementById('username').value = 'employee';
            document.getElementById('password').value = '123456';
            document.getElementById('department').value = 'hr';
        }

        // تسجيل الخروج
        function logout() {
            document.getElementById('dashboardPage').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');
            document.getElementById('loginForm').reset();
        }

        // عرض الأقسام
        function showSection(section) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('[id$="Section"]').forEach(el => {
                el.classList.add('hidden');
            });
            
            // إزالة active من جميع الروابط
            document.querySelectorAll('.nav-link').forEach(el => {
                el.classList.remove('active');
            });
            
            // عرض القسم المطلوب
            document.getElementById(section + 'Section').classList.remove('hidden');
            
            // إضافة active للرابط
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
