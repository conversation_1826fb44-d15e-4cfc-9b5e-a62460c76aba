#!/usr/bin/env python3
"""
تشغيل سريع لنظام إدارة الملفات مع اختيار منفذ متاح تلقائياً
Quick Start for File Management System with Auto Port Selection
"""

import os
import sys
import socket
import subprocess
from pathlib import Path

def is_port_available(port):
    """التحقق من توفر المنفذ"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port():
    """العثور على منفذ متاح"""
    preferred_ports = [8080, 8000, 8888, 9000, 9080, 7000, 7080, 6000, 6080, 5555]
    
    for port in preferred_ports:
        if is_port_available(port):
            return port
    
    # البحث في نطاق أوسع
    for port in range(8000, 10000):
        if is_port_available(port):
            return port
    
    return None

def check_python():
    """التحقق من وجود Python"""
    try:
        result = subprocess.run([sys.executable, '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ {version}")
            return True
    except:
        pass
    
    print("❌ Python غير متاح")
    return False

def install_requirements():
    """تثبيت المتطلبات"""
    if not Path('requirements.txt').exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    print("📦 تثبيت المتطلبات...")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت المتطلبات: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def create_env_file(port):
    """إنشاء ملف .env مع المنفذ المحدد"""
    env_content = f"""# إعدادات نظام إدارة الملفات - تم إنشاؤه تلقائياً
FLASK_CONFIG=development
SECRET_KEY=dev-secret-key-change-in-production
DATABASE_URL=sqlite:///file_management.db
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads
PERMANENT_SESSION_LIFETIME=7200
DEFAULT_LANGUAGE=ar
PORT={port}
HOST=0.0.0.0
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print(f"✅ تم إنشاء ملف .env مع المنفذ {port}")

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['uploads', 'instance']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✅ تم إنشاء المجلدات المطلوبة")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🚀 تشغيل سريع لنظام إدارة الملفات")
    print("=" * 60)
    
    # التحقق من Python
    if not check_python():
        input("اضغط Enter للخروج...")
        return
    
    # العثور على منفذ متاح
    print("🔍 البحث عن منفذ متاح...")
    port = find_available_port()
    
    if not port:
        print("❌ لم يتم العثور على منفذ متاح")
        input("اضغط Enter للخروج...")
        return
    
    print(f"✅ تم العثور على منفذ متاح: {port}")
    
    # إنشاء المجلدات
    create_directories()
    
    # إنشاء ملف .env
    create_env_file(port)
    
    # تثبيت المتطلبات
    if not install_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل التطبيق
    print("=" * 60)
    print("🎉 بدء تشغيل النظام...")
    print(f"🌐 الرابط: http://localhost:{port}")
    print("👤 بيانات المدير الافتراضي:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("   القسم: الإدارة")
    print("=" * 60)
    print("💡 للإيقاف: اضغط Ctrl+C")
    print("=" * 60)
    
    # تعيين متغير البيئة للمنفذ
    os.environ['PORT'] = str(port)
    
    try:
        # تشغيل التطبيق
        subprocess.run([sys.executable, 'app.py'])
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
