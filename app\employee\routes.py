import os
from flask import render_template, redirect, url_for, flash, request, send_file, current_app
from flask_login import login_required, current_user
from sqlalchemy import or_, desc
from werkzeug.utils import secure_filename
from app.employee import bp
from app.employee.forms import FileUploadForm, EditFileForm, SearchFileForm
from app.models import File, User
from app.utils import allowed_file, generate_unique_filename, get_file_size, get_file_type
from app import db

@bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة تحكم الموظف"""
    # إحصائيات الموظف
    stats = {
        'my_files': current_user.files.count(),
        'total_size': sum([f.file_size or 0 for f in current_user.files]),
        'recent_uploads': current_user.files.order_by(desc(File.uploaded_at)).limit(5).count()
    }
    
    # أحدث الملفات المرفوعة من قبل الموظف
    recent_files = current_user.files.order_by(desc(File.uploaded_at)).limit(10).all()
    
    # الملفات العامة من الأقسام الأخرى
    public_files = File.query.filter(
        File.is_public == True,
        File.uploader_id != current_user.id
    ).order_by(desc(File.uploaded_at)).limit(5).all()
    
    return render_template('employee/dashboard.html', 
                         stats=stats, 
                         recent_files=recent_files,
                         public_files=public_files)

@bp.route('/upload', methods=['GET', 'POST'])
@login_required
def upload_file():
    """رفع ملف جديد"""
    form = FileUploadForm()
    
    if form.validate_on_submit():
        file = form.file.data
        
        if file and allowed_file(file.filename):
            # إنشاء اسم ملف فريد
            original_filename = secure_filename(file.filename)
            unique_filename = generate_unique_filename(original_filename)
            
            # إنشاء مسار الحفظ
            upload_folder = current_app.config['UPLOAD_FOLDER']
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)
            
            file_path = os.path.join(upload_folder, unique_filename)
            
            try:
                # حفظ الملف
                file.save(file_path)
                
                # إنشاء سجل في قاعدة البيانات
                new_file = File(
                    title=form.title.data,
                    description=form.description.data,
                    filename=unique_filename,
                    original_filename=original_filename,
                    file_path=file_path,
                    file_size=get_file_size(file_path),
                    file_type=get_file_type(original_filename),
                    uploader_id=current_user.id,
                    department_id=current_user.department_id,
                    is_public=form.is_public.data
                )
                
                db.session.add(new_file)
                db.session.commit()
                
                flash('تم رفع الملف بنجاح!', 'success')
                return redirect(url_for('employee.my_files'))
                
            except Exception as e:
                # حذف الملف في حالة حدوث خطأ
                if os.path.exists(file_path):
                    os.remove(file_path)
                flash(f'حدث خطأ أثناء رفع الملف: {str(e)}', 'error')
        else:
            flash('نوع الملف غير مسموح', 'error')
    
    return render_template('employee/upload.html', form=form)

@bp.route('/my-files')
@login_required
def my_files():
    """عرض ملفات الموظف مع البحث والتصفية"""
    page = request.args.get('page', 1, type=int)
    search_form = SearchFileForm()

    query = current_user.files

    # تطبيق البحث النصي
    search_query = request.args.get('query', '').strip()
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                File.title.like(search_term),
                File.description.like(search_term),
                File.original_filename.like(search_term)
            )
        )

    # تطبيق فلتر نوع الملف
    file_type = request.args.get('file_type', '').strip()
    if file_type:
        query = query.filter(File.file_type == file_type)

    # تطبيق فلتر الملفات العامة/الخاصة
    is_public = request.args.get('is_public')
    if is_public == '1':
        query = query.filter(File.is_public == True)
    elif is_public == '0':
        query = query.filter(File.is_public == False)

    # ترتيب النتائج
    sort_by = request.args.get('sort_by', 'uploaded_at')
    sort_order = request.args.get('sort_order', 'desc')

    if sort_by == 'title':
        order_column = File.title
    elif sort_by == 'size':
        order_column = File.file_size
    elif sort_by == 'downloads':
        order_column = File.download_count
    else:
        order_column = File.uploaded_at

    if sort_order == 'asc':
        query = query.order_by(order_column.asc())
    else:
        query = query.order_by(order_column.desc())

    files = query.paginate(page=page, per_page=20, error_out=False)

    # إحصائيات الملفات
    my_stats = {
        'total_files': current_user.files.count(),
        'total_size': sum([f.file_size or 0 for f in current_user.files]),
        'public_files': current_user.files.filter_by(is_public=True).count(),
        'private_files': current_user.files.filter_by(is_public=False).count(),
        'file_types': db.session.query(File.file_type, db.func.count(File.id)).filter(
            File.uploader_id == current_user.id
        ).group_by(File.file_type).all()
    }

    return render_template('employee/my_files.html',
                         files=files,
                         search_form=search_form,
                         my_stats=my_stats,
                         current_filters={
                             'query': search_query,
                             'file_type': file_type,
                             'is_public': is_public,
                             'sort_by': sort_by,
                             'sort_order': sort_order
                         })

@bp.route('/files/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_file(id):
    """تعديل معلومات الملف"""
    file = File.query.get_or_404(id)
    
    # التحقق من أن الملف يخص الموظف الحالي
    if file.uploader_id != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذا الملف', 'error')
        return redirect(url_for('employee.my_files'))
    
    form = EditFileForm(obj=file)
    
    if form.validate_on_submit():
        file.title = form.title.data
        file.description = form.description.data
        file.is_public = form.is_public.data
        
        db.session.commit()
        flash('تم تحديث معلومات الملف بنجاح', 'success')
        return redirect(url_for('employee.my_files'))
    
    return render_template('employee/edit_file.html', form=form, file=file)

@bp.route('/files/<int:id>/download')
@login_required
def download_file(id):
    """تحميل ملف"""
    file = File.query.get_or_404(id)

    # التحقق من الصلاحيات
    if file.uploader_id != current_user.id and not file.is_public:
        flash('ليس لديك صلاحية لتحميل هذا الملف', 'error')
        return redirect(url_for('employee.my_files'))

    if not os.path.exists(file.file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('employee.my_files'))

    file.increment_download_count()
    return send_file(file.file_path, as_attachment=True,
                    download_name=file.original_filename)

@bp.route('/files/<int:id>/delete', methods=['POST'])
@login_required
def delete_file(id):
    """حذف ملف"""
    file = File.query.get_or_404(id)

    # التحقق من أن الملف يخص الموظف الحالي
    if file.uploader_id != current_user.id:
        flash('ليس لديك صلاحية لحذف هذا الملف', 'error')
        return redirect(url_for('employee.my_files'))

    # حذف الملف من النظام
    try:
        if os.path.exists(file.file_path):
            os.remove(file.file_path)
    except Exception as e:
        flash(f'خطأ في حذف الملف: {str(e)}', 'error')
        return redirect(url_for('employee.my_files'))

    # حذف السجل من قاعدة البيانات
    db.session.delete(file)
    db.session.commit()
    flash('تم حذف الملف بنجاح', 'success')
    return redirect(url_for('employee.my_files'))

@bp.route('/public-files')
@login_required
def public_files():
    """عرض الملفات العامة"""
    page = request.args.get('page', 1, type=int)
    search_form = SearchFileForm()

    query = File.query.filter(File.is_public == True)

    # تطبيق البحث
    if request.args.get('query'):
        search_term = f"%{request.args.get('query')}%"
        query = query.filter(
            or_(
                File.title.like(search_term),
                File.description.like(search_term),
                File.original_filename.like(search_term)
            )
        )

    files = query.order_by(File.uploaded_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('employee/public_files.html', files=files, search_form=search_form)

@bp.route('/profile')
@login_required
def profile():
    """عرض الملف الشخصي للموظف"""
    # حساب إجمالي حجم الملفات
    total_size = db.session.query(db.func.sum(File.file_size)).filter(
        File.uploader_id == current_user.id
    ).scalar() or 0

    # إحصائيات أنواع الملفات
    file_types_stats = db.session.query(
        File.file_type,
        db.func.count(File.id)
    ).filter(
        File.uploader_id == current_user.id
    ).group_by(File.file_type).all()

    return render_template('employee/profile.html',
                         user=current_user,
                         total_size=total_size,
                         file_types_stats=file_types_stats)
