#!/usr/bin/env python3
import os
import sys
import sqlite3
from flask import Flask, render_template, request, redirect, url_for, flash
from werkzeug.security import generate_password_hash, check_password_hash

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'dev-secret-key-change-in-production'

# إنشاء قاعدة البيانات
def init_db():
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    db_path = 'instance/file_management.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # إنشاء الجداول
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS department (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(80) NOT NULL,
            full_name VARCHAR(120) NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            department_id INTEGER NOT NULL,
            is_admin BOOLEAN DEFAULT FALSE,
            UNIQUE (username, department_id)
        )
    ''')
    
    # إضافة البيانات الأساسية
    cursor.execute("INSERT OR IGNORE INTO department (name, description) VALUES (?, ?)", 
                   ('الإدارة', 'قسم الإدارة العامة'))
    cursor.execute("INSERT OR IGNORE INTO department (name, description) VALUES (?, ?)", 
                   ('الموارد البشرية', 'قسم الموارد البشرية'))
    
    # إضافة المستخدم الإداري
    password_hash = generate_password_hash('admin123')
    cursor.execute('''
        INSERT OR IGNORE INTO user (username, full_name, password_hash, department_id, is_admin)
        VALUES (?, ?, ?, ?, ?)
    ''', ('admin', 'مدير النظام', password_hash, 1, True))
    
    conn.commit()
    conn.close()

# الصفحة الرئيسية
@app.route('/')
def index():
    return redirect(url_for('login'))

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        department_id = request.form['department']
        
        # التحقق من المستخدم
        conn = sqlite3.connect('instance/file_management.db')
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, username, full_name, password_hash, is_admin 
            FROM user WHERE username = ? AND department_id = ?
        ''', (username, department_id))
        user = cursor.fetchone()
        conn.close()
        
        if user and check_password_hash(user[3], password):
            flash('تم تسجيل الدخول بنجاح!', 'success')
            if user[4]:  # is_admin
                return redirect(url_for('admin_dashboard'))
            else:
                return redirect(url_for('employee_dashboard'))
        else:
            flash('بيانات تسجيل الدخول غير صحيحة', 'error')
    
    # جلب الأقسام
    conn = sqlite3.connect('instance/file_management.db')
    cursor = conn.cursor()
    cursor.execute('SELECT id, name FROM department')
    departments = cursor.fetchall()
    conn.close()
    
    return render_template('simple_login.html', departments=departments)

# لوحة تحكم المدير
@app.route('/admin')
def admin_dashboard():
    return '''
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>لوحة تحكم المدير</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <style>body{font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;}</style>
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white text-center">
                            <h3>🎉 مرحباً بك في نظام إدارة الملفات</h3>
                        </div>
                        <div class="card-body text-center">
                            <h4>تم تسجيل الدخول بنجاح كمدير!</h4>
                            <p class="text-muted">النظام يعمل بشكل صحيح الآن</p>
                            <div class="alert alert-success">
                                ✅ قاعدة البيانات تعمل<br>
                                ✅ تسجيل الدخول يعمل<br>
                                ✅ النظام جاهز للاستخدام
                            </div>
                            <a href="/login" class="btn btn-secondary">العودة لتسجيل الدخول</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

# لوحة تحكم الموظف
@app.route('/employee')
def employee_dashboard():
    return '''
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>لوحة الموظف</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <style>body{font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;}</style>
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-success text-white text-center">
                            <h3>👋 مرحباً بك أيها الموظف</h3>
                        </div>
                        <div class="card-body text-center">
                            <h4>تم تسجيل الدخول بنجاح!</h4>
                            <p class="text-muted">يمكنك الآن استخدام النظام</p>
                            <a href="/login" class="btn btn-secondary">العودة لتسجيل الدخول</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🔧 إنشاء قاعدة البيانات...")
    init_db()
    print("✅ تم إنشاء قاعدة البيانات")
    print("🚀 تشغيل الخادم...")
    print("🌐 الرابط: http://localhost:8080")
    print("👤 المدير: admin / admin123 / الإدارة")
    app.run(debug=True, host='0.0.0.0', port=8080)
