@echo off
chcp 65001 >nul
echo ================================================
echo           🔍 فحص حالة Python والنظام
echo ================================================
echo.

echo 🐍 فحص Python...
python --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Python مثبت ويعمل
    python --version
) else (
    echo ❌ Python غير مثبت أو غير مُعرَّف في PATH
    echo.
    echo 💡 الحلول:
    echo 1. ثبت Python من python.org
    echo 2. تأكد من تحديد "Add to PATH"
    echo 3. أعد تشغيل Command Prompt
    goto end
)

echo.
echo 📦 فحص المكتبات...
python -c "import flask; print('✅ Flask:', flask.__version__)" 2>nul || echo "❌ Flask غير مثبت"
python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy مثبت')" 2>nul || echo "❌ Flask-SQLAlchemy غير مثبت"
python -c "import flask_login; print('✅ Flask-Login مثبت')" 2>nul || echo "❌ Flask-Login غير مثبت"
python -c "import flask_wtf; print('✅ Flask-WTF مثبت')" 2>nul || echo "❌ Flask-WTF غير مثبت"

echo.
echo 📁 فحص الملفات...
if exist "app.py" (echo ✅ app.py موجود) else (echo ❌ app.py مفقود)
if exist "config.py" (echo ✅ config.py موجود) else (echo ❌ config.py مفقود)
if exist "create_db.py" (echo ✅ create_db.py موجود) else (echo ❌ create_db.py مفقود)
if exist "app\__init__.py" (echo ✅ app\__init__.py موجود) else (echo ❌ app\__init__.py مفقود)

echo.
echo 🗄️  فحص قاعدة البيانات...
if exist "instance\file_management.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo ❌ قاعدة البيانات مفقودة
    echo 💡 شغل: python create_db.py
)

echo.
echo 🌐 فحص المنفذ 8080...
netstat -an | find "8080" >nul
if %errorlevel% equ 0 (
    echo ⚠️  المنفذ 8080 مستخدم
    echo 💡 قد تحتاج لتغيير المنفذ أو إيقاف التطبيق الآخر
) else (
    echo ✅ المنفذ 8080 متاح
)

echo.
echo ================================================
echo                    📊 النتيجة
echo ================================================

python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo 🎉 النظام جاهز للتشغيل!
    echo.
    echo 🚀 للتشغيل:
    echo    python app.py
    echo.
    echo 🌐 ثم افتح:
    echo    http://localhost:8080
    echo.
    echo 👤 بيانات تسجيل الدخول:
    echo    admin / admin123 / الإدارة
) else (
    echo ⚠️  يجب تثبيت Python أولاً
    echo.
    echo 📥 للتثبيت:
    echo    شغل INSTALL_AND_RUN.bat
)

:end
echo.
pause
