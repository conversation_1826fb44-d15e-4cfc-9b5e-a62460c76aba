import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginMana<PERSON>
from config import config

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()

def create_app(config_name=None):
    """Application factory pattern"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    # Import and register blueprints
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

    from app.employee import bp as employee_bp
    app.register_blueprint(employee_bp, url_prefix='/employee')

    # Register template filters
    from app.utils import get_file_icon, format_file_size
    app.jinja_env.filters['get_file_icon'] = get_file_icon
    app.jinja_env.filters['format_file_size'] = format_file_size
    
    # Create database tables
    with app.app_context():
        db.create_all()
        
        # Create default admin user if not exists
        from app.models import User, Department
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            # Create default department
            admin_dept = Department(name='الإدارة', description='قسم الإدارة العامة')
            db.session.add(admin_dept)
            db.session.commit()
            
            # Create admin user
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                is_admin=True,
                department_id=admin_dept.id
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
    
    return app

if __name__ == '__main__':
    app = create_app()

    # قراءة المنفذ من متغيرات البيئة أو استخدام القيمة الافتراضية
    port = int(os.environ.get('PORT', 8080))
    host = os.environ.get('HOST', '0.0.0.0')

    print(f"🚀 تشغيل نظام إدارة الملفات على http://{host}:{port}")
    app.run(debug=True, host=host, port=port)
