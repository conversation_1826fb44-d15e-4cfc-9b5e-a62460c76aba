{% extends "base.html" %}

{% block title %}إدارة الملفات - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open me-2"></i>
        إدارة الملفات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#searchFilters">
                <i class="fas fa-filter me-1"></i>
                فلاتر البحث
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="collapse {% if current_filters.query or current_filters.department_id or current_filters.file_type %}show{% endif %}" id="searchFilters">
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-search me-2"></i>
                البحث والتصفية
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" id="searchForm">
                <div class="row g-3">
                    <!-- Search Query -->
                    <div class="col-md-6">
                        {{ search_form.query.label(class="form-label") }}
                        {{ search_form.query(class="form-control", value=current_filters.query or '') }}
                    </div>
                    
                    <!-- Department Filter -->
                    <div class="col-md-3">
                        {{ search_form.department_id.label(class="form-label") }}
                        {{ search_form.department_id(class="form-select") }}
                    </div>
                    
                    <!-- File Type Filter -->
                    <div class="col-md-3">
                        {{ search_form.file_type.label(class="form-label") }}
                        {{ search_form.file_type(class="form-select") }}
                    </div>
                    
                    <!-- Employee Filter -->
                    <div class="col-md-4">
                        {{ search_form.uploader_id.label(class="form-label") }}
                        {{ search_form.uploader_id(class="form-select") }}
                    </div>
                    
                    <!-- Access Type Filter -->
                    <div class="col-md-4">
                        {{ search_form.is_public.label(class="form-label") }}
                        {{ search_form.is_public(class="form-select") }}
                    </div>
                    
                    <!-- Date Range -->
                    <div class="col-md-2">
                        {{ search_form.date_from.label(class="form-label") }}
                        {{ search_form.date_from(class="form-control", value=current_filters.date_from or '') }}
                    </div>
                    
                    <div class="col-md-2">
                        {{ search_form.date_to.label(class="form-label") }}
                        {{ search_form.date_to(class="form-control", value=current_filters.date_to or '') }}
                    </div>
                    
                    <!-- Sort Options -->
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-6">
                                {{ search_form.sort_by.label(class="form-label") }}
                                {{ search_form.sort_by(class="form-select") }}
                            </div>
                            <div class="col-6">
                                {{ search_form.sort_order.label(class="form-label") }}
                                {{ search_form.sort_order(class="form-select") }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            {{ search_form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('admin.files') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                مسح الفلاتر
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Search Statistics -->
{% if current_filters.query or current_filters.department_id or current_filters.file_type %}
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4>{{ search_stats.total_files }}</h4>
                <small>ملف موجود</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ (search_stats.total_size / 1024 / 1024) | round(1) }} MB</h4>
                <small>إجمالي الحجم</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4>{{ search_stats.file_types | length }}</h4>
                <small>نوع ملف</small>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Files List -->
{% if files.items %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
            <i class="fas fa-list me-2"></i>
            الملفات ({{ files.total }} ملف)
        </h6>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-secondary" onclick="toggleView('table')">
                <i class="fas fa-list"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="toggleView('grid')">
                <i class="fas fa-th"></i>
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Table View -->
        <div id="tableView" class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>الملف</th>
                        <th>الموظف</th>
                        <th>القسم</th>
                        <th>تاريخ الرفع</th>
                        <th>الحجم</th>
                        <th>النوع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for file in files.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="{{ file.file_type | get_file_icon }} fa-lg text-primary me-2"></i>
                                <div>
                                    <strong>{{ file.title }}</strong><br>
                                    <small class="text-muted">{{ file.original_filename }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                {{ file.uploader.full_name }}<br>
                                <small class="text-muted">{{ file.uploader.username }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ file.department.name }}</span>
                        </td>
                        <td>
                            <small>{{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <small>{{ file.get_file_size_formatted() }}</small>
                        </td>
                        <td>
                            <span class="badge bg-info file-type-badge">{{ file.file_type }}</span>
                            {% if file.is_public %}
                            <span class="badge bg-success file-type-badge">عام</span>
                            {% else %}
                            <span class="badge bg-warning file-type-badge">خاص</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('admin.download_file', id=file.id) }}" 
                                   class="btn btn-outline-primary btn-sm" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info btn-sm" 
                                        data-bs-toggle="modal" data-bs-target="#fileModal{{ file.id }}" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <form method="POST" action="{{ url_for('admin.delete_file', id=file.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الملف؟')">
                                    <button type="submit" class="btn btn-outline-danger btn-sm" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if files.pages > 1 %}
<nav aria-label="صفحات الملفات" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if files.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.files', page=files.prev_num, **current_filters) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in files.iter_pages() %}
            {% if page_num %}
                {% if page_num != files.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.files', page=page_num, **current_filters) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if files.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.files', page=files.next_num, **current_filters) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- Empty State -->
<div class="text-center py-5">
    {% if current_filters.query or current_filters.department_id or current_filters.file_type %}
    <i class="fas fa-search fa-4x text-muted mb-4"></i>
    <h4 class="text-muted">لا توجد نتائج</h4>
    <p class="text-muted">لم يتم العثور على ملفات تطابق معايير البحث</p>
    <a href="{{ url_for('admin.files') }}" class="btn btn-outline-primary">
        <i class="fas fa-times me-1"></i>
        مسح الفلاتر
    </a>
    {% else %}
    <i class="fas fa-folder-open fa-4x text-muted mb-4"></i>
    <h4 class="text-muted">لا توجد ملفات</h4>
    <p class="text-muted">لم يتم رفع أي ملفات في النظام بعد</p>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Set current filter values
document.addEventListener('DOMContentLoaded', function() {
    // Set current values for select fields
    {% if current_filters.department_id %}
    document.querySelector('select[name="department_id"]').value = '{{ current_filters.department_id }}';
    {% endif %}
    
    {% if current_filters.file_type %}
    document.querySelector('select[name="file_type"]').value = '{{ current_filters.file_type }}';
    {% endif %}
    
    {% if current_filters.uploader_id %}
    document.querySelector('select[name="uploader_id"]').value = '{{ current_filters.uploader_id }}';
    {% endif %}
    
    {% if current_filters.is_public %}
    document.querySelector('select[name="is_public"]').value = '{{ current_filters.is_public }}';
    {% endif %}
    
    {% if current_filters.sort_by %}
    document.querySelector('select[name="sort_by"]').value = '{{ current_filters.sort_by }}';
    {% endif %}
    
    {% if current_filters.sort_order %}
    document.querySelector('select[name="sort_order"]').value = '{{ current_filters.sort_order }}';
    {% endif %}
});

function toggleView(viewType) {
    // This function can be implemented to switch between table and grid views
    console.log('Switching to ' + viewType + ' view');
}
</script>
{% endblock %}
