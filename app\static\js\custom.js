// Custom JavaScript for File Management System

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // File upload drag and drop functionality
    initializeFileUpload();
    
    // Search functionality
    initializeSearch();
    
    // Confirmation dialogs
    initializeConfirmations();
    
    // File size formatting
    formatFileSizes();
});

// File Upload Functions
function initializeFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    if (!uploadArea || !fileInput) return;
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
}

function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const fileInput = document.getElementById('fileInput');
        fileInput.files = files;
        handleFileSelect({ target: { files: files } });
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length === 0) return;
    
    const file = files[0];
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const uploadArea = document.getElementById('uploadArea');
    const titleInput = document.getElementById('title');
    
    if (filePreview && fileName && fileSize) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        
        // Auto-fill title if empty
        if (titleInput && !titleInput.value) {
            const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
            titleInput.value = nameWithoutExt;
        }
        
        uploadArea.style.display = 'none';
        filePreview.style.display = 'block';
        
        // Validate file
        validateFile(file);
    }
}

function validateFile(file) {
    const maxSize = 16 * 1024 * 1024; // 16MB
    const allowedTypes = ['txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar'];
    
    const fileExt = file.name.split('.').pop().toLowerCase();
    
    if (file.size > maxSize) {
        showAlert('حجم الملف كبير جداً. الحد الأقصى 16 ميجابايت.', 'danger');
        return false;
    }
    
    if (!allowedTypes.includes(fileExt)) {
        showAlert('نوع الملف غير مسموح.', 'danger');
        return false;
    }
    
    return true;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Search Functions
function initializeSearch() {
    const searchInputs = document.querySelectorAll('input[type="search"], input[name="query"]');
    
    searchInputs.forEach(input => {
        input.addEventListener('input', debounce(function(e) {
            const query = e.target.value;
            if (query.length >= 3 || query.length === 0) {
                // Auto-submit search after 500ms delay
                setTimeout(() => {
                    const form = e.target.closest('form');
                    if (form) {
                        form.submit();
                    }
                }, 500);
            }
        }, 300));
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Confirmation Dialogs
function initializeConfirmations() {
    const deleteButtons = document.querySelectorAll('[data-confirm]');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm') || 'هل أنت متأكد؟';
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
}

// Utility Functions
function showAlert(message, type = 'info') {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid') || document.body;
    container.insertBefore(alertContainer, container.firstChild);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        const alert = new bootstrap.Alert(alertContainer);
        alert.close();
    }, 5000);
}

function formatFileSizes() {
    const fileSizeElements = document.querySelectorAll('[data-file-size]');
    
    fileSizeElements.forEach(element => {
        const bytes = parseInt(element.getAttribute('data-file-size'));
        element.textContent = formatFileSize(bytes);
    });
}

// Progress Bar for File Uploads
function showUploadProgress() {
    const submitBtn = document.getElementById('submitBtn');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
        submitBtn.disabled = true;
    }
}

// Copy to Clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('تم النسخ إلى الحافظة', 'success');
    }, function(err) {
        showAlert('فشل في النسخ', 'danger');
    });
}

// Theme Toggle (if needed)
function toggleTheme() {
    const body = document.body;
    const currentTheme = body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
}

// Load saved theme
function loadTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.body.setAttribute('data-theme', savedTheme);
}

// Statistics Animation
function animateStats() {
    const statNumbers = document.querySelectorAll('.stats-card .h5, .stats-card-2 .h5, .stats-card-3 .h5, .stats-card-4 .h5');
    
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 50;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 30);
    });
}

// Initialize stats animation on dashboard
if (window.location.pathname.includes('dashboard')) {
    setTimeout(animateStats, 500);
}

// Mobile Menu Toggle
function toggleMobileMenu() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

// Keyboard Shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+U for upload (on employee pages)
    if (e.ctrlKey && e.key === 'u' && window.location.pathname.includes('employee')) {
        e.preventDefault();
        window.location.href = '/employee/upload';
    }
    
    // Ctrl+S for search
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        const searchInput = document.querySelector('input[name="query"]');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }
});

// Export functions for global use
window.FileManager = {
    showAlert,
    formatFileSize,
    copyToClipboard,
    toggleTheme,
    showUploadProgress
};
