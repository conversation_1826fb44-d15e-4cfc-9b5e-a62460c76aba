{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>
        الملف الشخصي
    </h1>
</div>

<div class="row">
    <!-- Profile Information -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header text-center">
                <div class="avatar mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center w-100 h-100">
                        {{ user.full_name[0] }}
                    </div>
                </div>
                <h5 class="mb-0">{{ user.full_name }}</h5>
                <small class="text-muted">{{ user.department.name }}</small>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>اسم المستخدم:</strong></td>
                        <td>{{ user.username }}</td>
                    </tr>
                    <tr>
                        <td><strong>البريد الإلكتروني:</strong></td>
                        <td>{{ user.email }}</td>
                    </tr>
                    <tr>
                        <td><strong>القسم:</strong></td>
                        <td>
                            <span class="badge bg-secondary">{{ user.department.name }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ التسجيل:</strong></td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>آخر تسجيل دخول:</strong></td>
                        <td>
                            {% if user.last_login %}
                            {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                            لم يسجل دخول من قبل
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            {% if user.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Statistics -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائياتي
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4>{{ user.files.count() }}</h4>
                                <small>إجمالي الملفات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4>{{ user.files.filter_by(is_public=True).count() }}</h4>
                                <small>ملفات عامة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4>{{ user.files.filter_by(is_public=False).count() }}</h4>
                                <small>ملفات خاصة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4>{{ (total_size // 1024 // 1024) if total_size else 0 }} MB</h4>
                                <small>إجمالي الحجم</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- File Types Chart -->
                <div class="mt-4">
                    <h6>أنواع الملفات</h6>
                    {% if file_types_stats %}
                    <div class="row">
                        {% for file_type, count in file_types_stats %}
                        <div class="col-md-4 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="{{ file_type | get_file_icon }} me-2 text-primary"></i>
                                <span class="flex-grow-1">{{ file_type }}</span>
                                <span class="badge bg-secondary">{{ count }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">لا توجد ملفات مرفوعة</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    النشاط الأخير
                </h6>
            </div>
            <div class="card-body">
                {% set recent_files = user.files.order_by(File.uploaded_at.desc()).limit(5).all() %}
                {% if recent_files %}
                <div class="list-group list-group-flush">
                    {% for file in recent_files %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="{{ file.file_type | get_file_icon }} me-3 text-primary"></i>
                            <div>
                                <h6 class="mb-0">{{ file.title }}</h6>
                                <small class="text-muted">{{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                        </div>
                        <div>
                            {% if file.is_public %}
                            <span class="badge bg-success me-2">عام</span>
                            {% endif %}
                            <a href="{{ url_for('employee.download_file', id=file.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="mt-3 text-center">
                    <a href="{{ url_for('employee.my_files') }}" class="btn btn-outline-primary">
                        <i class="fas fa-folder me-1"></i>
                        عرض جميع ملفاتي
                    </a>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد ملفات مرفوعة</p>
                    <a href="{{ url_for('employee.upload_file') }}" class="btn btn-primary">
                        <i class="fas fa-cloud-upload-alt me-1"></i>
                        رفع أول ملف
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
