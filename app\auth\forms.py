from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length, Email
from app.models import Department

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[
        DataRequired(message='اسم المستخدم مطلوب'),
        Length(min=3, max=80, message='اسم المستخدم يجب أن يكون بين 3 و 80 حرف')
    ])
    password = PasswordField('كلمة المرور', validators=[
        DataRequired(message='كلمة المرور مطلوبة'),
        Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    ])
    department = SelectField('القسم', coerce=int, validators=[
        DataRequired(message='يرجى اختيار القسم')
    ])
    submit = SubmitField('تسجيل الدخول')
    
    def __init__(self, *args, **kwargs):
        super(LoginForm, self).__init__(*args, **kwargs)
        # تحديث قائمة الأقسام
        self.department.choices = [(0, 'اختر القسم')] + [
            (dept.id, dept.name) for dept in Department.query.all()
        ]
