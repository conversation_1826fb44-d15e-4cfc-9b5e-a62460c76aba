@echo off
echo ================================================
echo           نظام إدارة الملفات
echo        File Management System
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير متاح في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

echo تم العثور على Python...
echo.

REM إنشاء البيئة الافتراضية إذا لم تكن موجودة
if not exist "venv" (
    echo إنشاء البيئة الافتراضية...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
)

REM تفعيل البيئة الافتراضية
echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

REM تثبيت المتطلبات
echo تثبيت المتطلبات...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

REM إنشاء المجلدات المطلوبة
if not exist "uploads" mkdir uploads
if not exist "instance" mkdir instance

echo.
echo ================================================
echo تشغيل النظام...
echo الرابط: http://localhost:8080 (أو المنفذ المتاح)
echo المدير الافتراضي:
echo   اسم المستخدم: admin
echo   كلمة المرور: admin123
echo   القسم: الإدارة
echo ================================================
echo للإيقاف: اضغط Ctrl+C
echo ================================================
echo.

REM تشغيل التطبيق
python app.py

pause
