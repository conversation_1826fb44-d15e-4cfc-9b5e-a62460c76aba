import os
import sqlite3
from werkzeug.security import generate_password_hash

# إنشاء مجلد instance
if not os.path.exists('instance'):
    os.makedirs('instance')

# إنشاء قاعدة البيانات
db_path = 'instance/file_management.db'
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# إنشاء جدول الأقسام
cursor.execute('''
    CREATE TABLE IF NOT EXISTS departments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
''')

# إنشاء جدول المستخدمين
cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(80) NOT NULL UNIQUE,
        email VARCHAR(120) NOT NULL UNIQUE,
        full_name VARCHAR(200) NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        department_id INTEGER NOT NULL,
        is_admin BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments (id)
    )
''')

# إنشاء جدول الملفات
cursor.execute('''
    CREATE TABLE IF NOT EXISTS files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        filename VARCHAR(255) NOT NULL,
        original_filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INTEGER,
        file_type VARCHAR(50),
        is_public BOOLEAN DEFAULT FALSE,
        uploaded_by INTEGER NOT NULL,
        department_id INTEGER NOT NULL,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (uploaded_by) REFERENCES users (id),
        FOREIGN KEY (department_id) REFERENCES departments (id)
    )
''')

# إضافة الأقسام
departments = [
    ('الإدارة', 'قسم الإدارة العامة'),
    ('الموارد البشرية', 'قسم الموارد البشرية'),
    ('المالية', 'قسم المالية والمحاسبة'),
    ('تقنية المعلومات', 'قسم تقنية المعلومات')
]

for name, desc in departments:
    cursor.execute("INSERT OR IGNORE INTO departments (name, description) VALUES (?, ?)", (name, desc))

# إضافة المستخدم الإداري
cursor.execute("SELECT id FROM departments WHERE name = 'الإدارة'")
admin_dept_id = cursor.fetchone()[0]

password_hash = generate_password_hash('admin123')
cursor.execute('''
    INSERT OR IGNORE INTO users (username, full_name, email, password_hash, department_id, is_admin, is_active)
    VALUES (?, ?, ?, ?, ?, ?, ?)
''', ('admin', 'مدير النظام', '<EMAIL>', password_hash, admin_dept_id, True, True))

conn.commit()
conn.close()

print("تم إنشاء قاعدة البيانات بنجاح!")
print("المستخدم: admin")
print("كلمة المرور: admin123")
print("القسم: الإدارة")
