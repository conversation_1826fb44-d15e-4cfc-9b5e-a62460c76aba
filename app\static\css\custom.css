/* Custom styles for File Management System */

/* RTL Support */
body {
    direction: rtl;
    text-align: right;
}

/* Custom Colors */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* File Type Icons Colors */
.file-icon.text-primary { color: var(--primary-color) !important; }
.file-icon.text-success { color: var(--success-color) !important; }
.file-icon.text-warning { color: var(--warning-color) !important; }
.file-icon.text-danger { color: var(--danger-color) !important; }
.file-icon.text-info { color: var(--info-color) !important; }

/* Upload Area Styles */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #fafafa;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: #f0f8ff;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: var(--success-color);
    background-color: #f0fff0;
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Stats Cards */
.stats-card,
.stats-card-2,
.stats-card-3,
.stats-card-4 {
    border: none;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.stats-card:hover,
.stats-card-2:hover,
.stats-card-3:hover,
.stats-card-4:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2) !important;
}

/* Button Styles */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Table Styles */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
    border-left: 1px solid #e9ecef;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.sidebar .nav-link {
    color: #495057;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
    transform: translateX(-3px);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.3);
}

.sidebar .nav-link i {
    width: 20px;
    margin-left: 0.75rem;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.75rem;
    border-right: 4px solid;
}

.alert-success {
    border-right-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.1);
}

.alert-danger {
    border-right-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.alert-warning {
    border-right-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.alert-info {
    border-right-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.1);
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
}

/* File Type Badges */
.file-type-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

/* Avatar Styles */
.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Pagination */
.pagination .page-link {
    border-radius: 0.5rem;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-top: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: 0;
        width: 100%;
        height: calc(100vh - 56px);
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .stats-card .h5 {
        font-size: 1.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background-color: var(--card-bg);
        border-color: #404040;
    }
    
    .table {
        color: var(--text-color);
    }
    
    .table th {
        background-color: #404040;
        color: var(--text-color);
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .dropdown,
    .pagination {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    main {
        width: 100% !important;
        max-width: 100% !important;
    }
}
