{% extends "base.html" %}

{% block title %}رفع ملف جديد - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cloud-upload-alt me-2"></i>
        رفع ملف جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('employee.my_files') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة إلى ملفاتي
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-upload me-2"></i>
                    معلومات الملف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    {{ form.hidden_tag() }}
                    
                    <!-- File Upload Area -->
                    <div class="mb-4">
                        {{ form.file.label(class="form-label") }}
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>اسحب الملف هنا أو انقر للاختيار</h5>
                            <p class="text-muted">
                                الحد الأقصى لحجم الملف: 16 MB<br>
                                الأنواع المسموحة: {{ config.ALLOWED_EXTENSIONS | join(', ') }}
                            </p>
                            {{ form.file(class="form-control", style="display: none;", id="fileInput") }}
                        </div>
                        {% if form.file.errors %}
                            <div class="text-danger mt-2">
                                {% for error in form.file.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <!-- File Preview -->
                        <div id="filePreview" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file fa-2x me-3"></i>
                                    <div>
                                        <strong id="fileName"></strong><br>
                                        <small id="fileSize" class="text-muted"></small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger ms-auto" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- File Title -->
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else ""), placeholder="أدخل عنوان الملف") }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- File Description -->
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), 
                                          rows="4", placeholder="أدخل وصف الملف (اختياري)") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">يمكنك إضافة وصف مفصل للملف لتسهيل البحث عنه لاحقاً</div>
                    </div>
                    
                    <!-- Public File Option -->
                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_public(class="form-check-input") }}
                            {{ form.is_public.label(class="form-check-label") }}
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            إذا تم تفعيل هذا الخيار، سيتمكن جميع الموظفين من رؤية وتحميل هذا الملف
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('employee.dashboard') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {{ form.submit(class="btn btn-primary", id="submitBtn") }}
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Upload Guidelines -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    إرشادات الرفع
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>نصائح مهمة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-left text-primary me-2"></i>استخدم أسماء ملفات واضحة ومفهومة</li>
                            <li><i class="fas fa-arrow-left text-primary me-2"></i>أضف وصف مفصل للملف</li>
                            <li><i class="fas fa-arrow-left text-primary me-2"></i>تأكد من نوع الملف قبل الرفع</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-file-alt text-info me-2"></i>الأنواع المسموحة:</h6>
                        <div class="d-flex flex-wrap">
                            {% for ext in config.ALLOWED_EXTENSIONS %}
                            <span class="badge bg-light text-dark me-1 mb-1">{{ ext.upper() }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const removeFile = document.getElementById('removeFile');
    const titleInput = document.getElementById('title');
    const submitBtn = document.getElementById('submitBtn');
    
    // Upload area click
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('border-primary');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    });
    
    // File input change
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });
    
    // Remove file
    removeFile.addEventListener('click', function() {
        fileInput.value = '';
        filePreview.style.display = 'none';
        uploadArea.style.display = 'block';
        titleInput.value = '';
    });
    
    function handleFileSelect(file) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        
        // Auto-fill title if empty
        if (!titleInput.value) {
            const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
            titleInput.value = nameWithoutExt;
        }
        
        uploadArea.style.display = 'none';
        filePreview.style.display = 'block';
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Form submission with progress
    document.getElementById('uploadForm').addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}
