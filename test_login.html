<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة تسجيل الدخول - نظام إدارة الملفات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .card {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
            border-radius: 1rem;
        }
        .card-header {
            border-radius: 1rem 1rem 0 0 !important;
        }
        .btn {
            border-radius: 0.5rem;
        }
        .form-control, .form-select {
            border-radius: 0.5rem;
        }
        .demo-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <!-- رسالة حالة النظام -->
                <div class="alert alert-info text-center mb-4">
                    <h5><i class="fas fa-info-circle me-2"></i>حالة النظام</h5>
                    <p class="mb-0">هذا اختبار لواجهة تسجيل الدخول</p>
                    <small>النظام يحتاج تشغيل الخادم للعمل الكامل</small>
                </div>

                <div class="card">
                    <div class="card-header text-center bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </h4>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>اسم المستخدم
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="أدخل اسم المستخدم" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="أدخل كلمة المرور" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="department" class="form-label">
                                    <i class="fas fa-building me-2"></i>القسم
                                </label>
                                <select class="form-select" id="department" name="department" required>
                                    <option value="">اختر القسم</option>
                                    <option value="1">الإدارة</option>
                                    <option value="2">الموارد البشرية</option>
                                    <option value="3">المالية</option>
                                    <option value="4">تقنية المعلومات</option>
                                    <option value="5">التسويق</option>
                                    <option value="6">العمليات</option>
                                </select>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-center">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            نظام آمن لإدارة الملفات
                        </small>
                    </div>
                </div>
                
                <!-- معلومات تجريبية -->
                <div class="card mt-3 demo-info">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-key me-2"></i>
                            بيانات تجريبية للاختبار
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <strong><i class="fas fa-user-tie me-2"></i>المدير:</strong><br>
                                    <small>
                                        اسم المستخدم: <code>admin</code><br>
                                        كلمة المرور: <code>admin123</code><br>
                                        القسم: <code>الإدارة</code>
                                    </small>
                                </div>
                                <div>
                                    <strong><i class="fas fa-user me-2"></i>موظف تجريبي:</strong><br>
                                    <small>
                                        اسم المستخدم: <code>employee1</code><br>
                                        كلمة المرور: <code>123456</code><br>
                                        القسم: <code>الموارد البشرية</code>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تعليمات التشغيل -->
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تعليمات التشغيل
                        </h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>تأكد من تثبيت Python 3.8+</li>
                            <li>شغل: <code>pip install -r requirements.txt</code></li>
                            <li>شغل: <code>python fix_database.py</code></li>
                            <li>شغل: <code>python app.py</code></li>
                            <li>افتح: <code>http://localhost:8080</code></li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const department = document.getElementById('department').value;
            
            if (!username || !password || !department) {
                alert('يرجى ملء جميع الحقول');
                return;
            }
            
            // محاكاة تسجيل الدخول
            if (username === 'admin' && password === 'admin123' && department === '1') {
                alert('✅ تم تسجيل الدخول بنجاح كمدير!\n\nملاحظة: هذا مجرد اختبار للواجهة.\nلتشغيل النظام الكامل، يرجى اتباع التعليمات أدناه.');
            } else if (username === 'employee1' && password === '123456' && department === '2') {
                alert('✅ تم تسجيل الدخول بنجاح كموظف!\n\nملاحظة: هذا مجرد اختبار للواجهة.\nلتشغيل النظام الكامل، يرجى اتباع التعليمات أدناه.');
            } else {
                alert('❌ بيانات تسجيل الدخول غير صحيحة\n\nاستخدم البيانات التجريبية المعروضة أدناه');
            }
        });

        // ملء البيانات التجريبية عند النقر
        function fillAdminData() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            document.getElementById('department').value = '1';
        }

        function fillEmployeeData() {
            document.getElementById('username').value = 'employee1';
            document.getElementById('password').value = '123456';
            document.getElementById('department').value = '2';
        }

        // إضافة أزرار الملء السريع
        document.addEventListener('DOMContentLoaded', function() {
            const demoCard = document.querySelector('.demo-info .card-body');
            demoCard.innerHTML += `
                <div class="mt-3 text-center">
                    <button type="button" class="btn btn-sm btn-outline-light me-2" onclick="fillAdminData()">
                        <i class="fas fa-user-tie me-1"></i>ملء بيانات المدير
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-light" onclick="fillEmployeeData()">
                        <i class="fas fa-user me-1"></i>ملء بيانات الموظف
                    </button>
                </div>
            `;
        });
    </script>
</body>
</html>
