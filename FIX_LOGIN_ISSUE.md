# 🔧 حل مشكلة صفحة تسجيل الدخول

## المشكلة
صفحة تسجيل الدخول لا تعمل وتظهر أخطاء في العرض.

## السبب
قاعدة البيانات غير موجودة أو لا تحتوي على البيانات الأساسية.

## الحل السريع ⚡

### الطريقة الأولى: التشغيل التلقائي
```bash
# انقر نقراً مزدوجاً على أي من الملفات التالية:
simple_fix.bat      # إصلاح وتشغيل
quick_start.bat     # تشغيل سريع محدث
```

### الطريقة الثانية: الإصلاح اليدوي
```bash
# 1. إصلاح قاعدة البيانات
python fix_database.py

# 2. تشغيل النظام
python app.py
```

### الطريقة الثالثة: فحص المشكلة أولاً
```bash
# فحص حالة النظام
python check_system.py

# ثم الإصلاح حسب النتائج
python fix_database.py
```

## ما يفعله الإصلاح 🔧

1. **إنشاء مجلد instance** إذا لم يكن موجوداً
2. **إنشاء قاعدة البيانات** مع الجداول المطلوبة:
   - جدول الأقسام (departments)
   - جدول المستخدمين (users)
   - جدول الملفات (files)

3. **إضافة الأقسام الافتراضية:**
   - الإدارة
   - الموارد البشرية
   - المالية
   - تقنية المعلومات
   - التسويق
   - العمليات

4. **إنشاء المستخدمين الافتراضيين:**
   - **المدير:** admin / admin123 / الإدارة
   - **موظف تجريبي:** employee1 / 123456 / الموارد البشرية

## بعد الإصلاح ✅

1. **افتح المتصفح** وانتقل إلى: `http://localhost:8080`
2. **سجل دخول** باستخدام:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`
   - القسم: `الإدارة`

## إذا استمرت المشكلة 🆘

### فحص الأخطاء:
```bash
# فحص حالة النظام
python check_system.py

# فحص المنافذ المتاحة
python config_ports.py --status
```

### حلول إضافية:

#### 1. مشكلة المكتبات:
```bash
pip install -r requirements.txt
```

#### 2. مشكلة المنفذ:
```bash
# تغيير المنفذ في ملف .env
PORT=9000
```

#### 3. إعادة إنشاء قاعدة البيانات:
```bash
# احذف قاعدة البيانات الحالية
del instance\file_management.db

# أعد إنشاؤها
python fix_database.py
```

#### 4. مشكلة الصلاحيات:
```bash
# تشغيل كمدير (Windows)
# انقر بالزر الأيمن على Command Prompt
# اختر "Run as administrator"
```

## الملفات المساعدة 📁

- `fix_database.py` - إصلاح قاعدة البيانات
- `check_system.py` - فحص حالة النظام
- `simple_fix.bat` - إصلاح وتشغيل سريع
- `config_ports.py` - إدارة المنافذ
- `quick_start.py` - تشغيل سريع محدث

## نصائح مهمة 💡

1. **تأكد من تثبيت Python 3.8+**
2. **شغل الأوامر في مجلد المشروع**
3. **تأكد من عدم تشغيل تطبيق آخر على نفس المنفذ**
4. **استخدم المستخدم الإداري للدخول الأول**
5. **أنشئ أقسام جديدة قبل إضافة موظفين**

---

**إذا اتبعت هذه الخطوات، ستحل المشكلة بإذن الله! 🎉**
