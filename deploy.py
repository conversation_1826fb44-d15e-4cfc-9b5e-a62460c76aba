#!/usr/bin/env python3
"""
ملف نشر نظام إدارة الملفات
Production Deployment Script

هذا الملف يساعد في نشر النظام في بيئة الإنتاج
"""

import os
import sys
import subprocess
import secrets
from pathlib import Path

def generate_secret_key():
    """إنشاء مفتاح تشفير عشوائي"""
    return secrets.token_urlsafe(32)

def create_production_env():
    """إنشاء ملف .env للإنتاج"""
    secret_key = generate_secret_key()
    
    env_content = f"""# إعدادات الإنتاج - نظام إدارة الملفات
# Production Configuration - File Management System

# بيئة التشغيل
FLASK_CONFIG=production

# مفتاح التشفير (تم إنشاؤه تلقائياً)
SECRET_KEY={secret_key}

# قاعدة البيانات (يمكن تغييرها لاستخدام MySQL/PostgreSQL)
DATABASE_URL=sqlite:///file_management_prod.db

# إعدادات الملفات
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# إعدادات الجلسة (ساعتان)
PERMANENT_SESSION_LIFETIME=7200

# إعدادات اللغة
DEFAULT_LANGUAGE=ar

# إعدادات الأمان
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
"""
    
    with open('.env.production', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ تم إنشاء ملف .env.production")
    print(f"🔑 مفتاح التشفير: {secret_key}")

def create_gunicorn_config():
    """إنشاء ملف تكوين Gunicorn"""
    config_content = """# Gunicorn Configuration
import multiprocessing

# Server socket
bind = "0.0.0.0:8000"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process naming
proc_name = "file_management_system"

# Server mechanics
preload_app = True
daemon = False
pidfile = "gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# SSL (uncomment if using HTTPS)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
"""
    
    with open('gunicorn.conf.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ تم إنشاء ملف gunicorn.conf.py")

def create_systemd_service():
    """إنشاء ملف خدمة systemd"""
    current_dir = os.getcwd()
    service_content = f"""[Unit]
Description=File Management System
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory={current_dir}
Environment=PATH={current_dir}/venv/bin
ExecStart={current_dir}/venv/bin/gunicorn --config gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    with open('file-management.service', 'w', encoding='utf-8') as f:
        f.write(service_content)
    
    print("✅ تم إنشاء ملف file-management.service")
    print("📝 لتثبيت الخدمة:")
    print(f"   sudo cp file-management.service /etc/systemd/system/")
    print(f"   sudo systemctl enable file-management")
    print(f"   sudo systemctl start file-management")

def create_nginx_config():
    """إنشاء ملف تكوين Nginx"""
    nginx_content = """server {
    listen 80;
    server_name your-domain.com;  # غير هذا إلى نطاقك
    
    # Redirect HTTP to HTTPS (uncomment if using SSL)
    # return 301 https://$server_name$request_uri;
    
    client_max_body_size 20M;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files
    location /static/ {
        alias /path/to/your/app/app/static/;  # غير هذا إلى المسار الصحيح
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}

# HTTPS configuration (uncomment if using SSL)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     
#     ssl_certificate /path/to/certificate.crt;
#     ssl_certificate_key /path/to/private.key;
#     
#     # SSL settings
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     client_max_body_size 20M;
#     
#     location / {
#         proxy_pass http://127.0.0.1:8000;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }
# }
"""
    
    with open('nginx.conf', 'w', encoding='utf-8') as f:
        f.write(nginx_content)
    
    print("✅ تم إنشاء ملف nginx.conf")
    print("📝 لتثبيت تكوين Nginx:")
    print(f"   sudo cp nginx.conf /etc/nginx/sites-available/file-management")
    print(f"   sudo ln -s /etc/nginx/sites-available/file-management /etc/nginx/sites-enabled/")
    print(f"   sudo nginx -t")
    print(f"   sudo systemctl reload nginx")

def install_production_requirements():
    """تثبيت متطلبات الإنتاج"""
    print("📦 تثبيت متطلبات الإنتاج...")
    
    production_requirements = """Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
WTForms==3.0.1
Werkzeug==2.3.7
python-dotenv==1.0.0
Pillow==10.0.1
gunicorn==21.2.0
psycopg2-binary==2.9.7
PyMySQL==1.1.0
"""
    
    with open('requirements-production.txt', 'w') as f:
        f.write(production_requirements)
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements-production.txt'])
        print("✅ تم تثبيت متطلبات الإنتاج")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت متطلبات الإنتاج")

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['logs', 'backups', 'uploads', 'instance']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 تم إنشاء مجلد: {directory}")

def main():
    """الدالة الرئيسية للنشر"""
    print("=" * 60)
    print("🚀 إعداد نظام إدارة الملفات للإنتاج")
    print("=" * 60)
    
    # إنشاء المجلدات
    create_directories()
    
    # إنشاء ملفات التكوين
    create_production_env()
    create_gunicorn_config()
    create_systemd_service()
    create_nginx_config()
    
    # تثبيت المتطلبات
    install_production_requirements()
    
    print("=" * 60)
    print("✅ تم إعداد النظام للإنتاج!")
    print("=" * 60)
    print("📋 الخطوات التالية:")
    print("1. راجع ملف .env.production وعدل الإعدادات حسب الحاجة")
    print("2. غير نطاق الخادم في nginx.conf")
    print("3. ثبت خدمة systemd:")
    print("   sudo cp file-management.service /etc/systemd/system/")
    print("   sudo systemctl enable file-management")
    print("4. ثبت تكوين Nginx")
    print("5. شغل الخدمة:")
    print("   sudo systemctl start file-management")
    print("=" * 60)

if __name__ == '__main__':
    main()
