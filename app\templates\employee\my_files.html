{% extends "base.html" %}

{% block title %}ملفاتي - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder me-2"></i>
        ملفاتي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('employee.upload_file') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>
                رفع ملف جديد
            </a>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والتصفية
        </h6>
        <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#advancedSearch">
            <i class="fas fa-cog me-1"></i>
            خيارات متقدمة
        </button>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <!-- Basic Search -->
            <div class="col-md-8">
                {{ search_form.query(class="form-control", value=current_filters.query or '') }}
            </div>
            <div class="col-md-4">
                <div class="d-grid">
                    {{ search_form.submit(class="btn btn-primary") }}
                </div>
            </div>

            <!-- Advanced Search Options -->
            <div class="collapse {% if current_filters.file_type or current_filters.is_public %}show{% endif %}" id="advancedSearch">
                <div class="col-12"><hr></div>

                <div class="col-md-3">
                    {{ search_form.file_type.label(class="form-label") }}
                    {{ search_form.file_type(class="form-select") }}
                </div>

                <div class="col-md-3">
                    {{ search_form.is_public.label(class="form-label") }}
                    {{ search_form.is_public(class="form-select") }}
                </div>

                <div class="col-md-3">
                    {{ search_form.sort_by.label(class="form-label") }}
                    {{ search_form.sort_by(class="form-select") }}
                </div>

                <div class="col-md-3">
                    {{ search_form.sort_order.label(class="form-label") }}
                    {{ search_form.sort_order(class="form-select") }}
                </div>

                <div class="col-12">
                    <a href="{{ url_for('employee.my_files') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- My Statistics -->
{% if my_stats %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h5>{{ my_stats.total_files }}</h5>
                <small>إجمالي الملفات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h5>{{ (my_stats.total_size / 1024 / 1024) | round(1) }} MB</h5>
                <small>إجمالي الحجم</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h5>{{ my_stats.public_files }}</h5>
                <small>ملفات عامة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h5>{{ my_stats.private_files }}</h5>
                <small>ملفات خاصة</small>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% if files.items %}
<!-- Files Grid -->
<div class="row">
    {% for file in files.items %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="{{ file.file_type | get_file_icon }} fa-lg text-primary me-2"></i>
                    <h6 class="mb-0">{{ file.title }}</h6>
                </div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                            data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="{{ url_for('employee.download_file', id=file.id) }}">
                                <i class="fas fa-download me-2"></i>تحميل
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{{ url_for('employee.edit_file', id=file.id) }}">
                                <i class="fas fa-edit me-2"></i>تعديل
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ url_for('employee.delete_file', id=file.id) }}" 
                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا الملف؟')">
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="fas fa-trash me-2"></i>حذف
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <p class="card-text">
                    {% if file.description %}
                        {{ file.description[:100] }}{% if file.description|length > 100 %}...{% endif %}
                    {% else %}
                        <span class="text-muted">لا يوجد وصف</span>
                    {% endif %}
                </p>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <small class="text-muted d-block">الحجم</small>
                            <strong>{{ file.get_file_size_formatted() }}</strong>
                        </div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">التحميلات</small>
                        <strong>{{ file.download_count }}</strong>
                    </div>
                </div>
                
                <div class="mt-3">
                    <span class="badge bg-info file-type-badge">{{ file.file_type }}</span>
                    {% if file.is_public %}
                    <span class="badge bg-success file-type-badge">عام</span>
                    {% else %}
                    <span class="badge bg-secondary file-type-badge">خاص</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    <i class="fas fa-calendar me-1"></i>
                    {{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                </small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if files.pages > 1 %}
<nav aria-label="صفحات الملفات">
    <ul class="pagination justify-content-center">
        {% if files.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('employee.my_files', page=files.prev_num, query=request.args.get('query', '')) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in files.iter_pages() %}
            {% if page_num %}
                {% if page_num != files.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('employee.my_files', page=page_num, query=request.args.get('query', '')) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if files.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('employee.my_files', page=files.next_num, query=request.args.get('query', '')) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- Empty State -->
<div class="text-center py-5">
    {% if request.args.get('query') %}
    <i class="fas fa-search fa-4x text-muted mb-4"></i>
    <h4 class="text-muted">لا توجد نتائج</h4>
    <p class="text-muted">لم يتم العثور على ملفات تطابق البحث "{{ request.args.get('query') }}"</p>
    <a href="{{ url_for('employee.my_files') }}" class="btn btn-outline-primary">
        <i class="fas fa-times me-1"></i>
        إلغاء البحث
    </a>
    {% else %}
    <i class="fas fa-folder-open fa-4x text-muted mb-4"></i>
    <h4 class="text-muted">لا توجد ملفات</h4>
    <p class="text-muted">لم تقم برفع أي ملفات بعد</p>
    <a href="{{ url_for('employee.upload_file') }}" class="btn btn-primary">
        <i class="fas fa-cloud-upload-alt me-1"></i>
        رفع أول ملف
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}
