{% extends "base.html" %}

{% block title %}لوحة الموظف - نظام إدارة الملفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-home me-2"></i>
        مرحباً، {{ current_user.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('employee.upload_file') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-cloud-upload-alt me-1"></i>
                رفع ملف جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            ملفاتي
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.my_files }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card stats-card-2 h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي الحجم
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ (stats.total_size / 1024 / 1024) | round(1) }} MB
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card stats-card-3 h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            القسم
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ current_user.department.name }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Files and Public Files -->
<div class="row">
    <!-- My Recent Files -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-clock me-2"></i>
                    ملفاتي الأخيرة
                </h6>
                <a href="{{ url_for('employee.my_files') }}" class="btn btn-sm btn-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_files %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الملف</th>
                                <th>تاريخ الرفع</th>
                                <th>الحجم</th>
                                <th>النوع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file in recent_files %}
                            <tr>
                                <td>
                                    <i class="{{ file.file_type | get_file_icon }} file-icon text-primary"></i>
                                    <strong>{{ file.title }}</strong><br>
                                    <small class="text-muted">{{ file.original_filename }}</small>
                                </td>
                                <td>
                                    <small>{{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    <small>{{ file.get_file_size_formatted() }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-info file-type-badge">{{ file.file_type }}</span>
                                    {% if file.is_public %}
                                    <span class="badge bg-success file-type-badge">عام</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('employee.download_file', id=file.id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <a href="{{ url_for('employee.edit_file', id=file.id) }}" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لم تقم برفع أي ملفات بعد</p>
                    <a href="{{ url_for('employee.upload_file') }}" class="btn btn-primary">
                        <i class="fas fa-cloud-upload-alt me-1"></i>
                        رفع أول ملف
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Public Files -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-share-alt me-2"></i>
                    الملفات العامة
                </h6>
                <a href="{{ url_for('employee.public_files') }}" class="btn btn-sm btn-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if public_files %}
                {% for file in public_files %}
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <i class="{{ file.file_type | get_file_icon }} fa-2x text-primary"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0">{{ file.title }}</h6>
                        <small class="text-muted">
                            بواسطة {{ file.uploader.full_name }} - {{ file.department.name }}
                        </small>
                    </div>
                    <div class="text-end">
                        <a href="{{ url_for('employee.download_file', id=file.id) }}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download"></i>
                        </a>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-share-alt fa-2x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد ملفات عامة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('employee.upload_file') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-cloud-upload-alt fa-2x mb-2"></i><br>
                            رفع ملف جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('employee.my_files') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-folder fa-2x mb-2"></i><br>
                            ملفاتي
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('employee.public_files') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-share-alt fa-2x mb-2"></i><br>
                            الملفات العامة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('employee.profile') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-user fa-2x mb-2"></i><br>
                            الملف الشخصي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
