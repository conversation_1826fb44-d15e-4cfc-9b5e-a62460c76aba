#!/usr/bin/env python3
"""
إعداد وتثبيت نظام إدارة الملفات
File Management System Setup

هذا الملف يقوم بإعداد النظام وتثبيت المتطلبات
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {platform.python_version()}")
        sys.exit(1)
    else:
        print(f"✅ Python {platform.python_version()} - متوافق")

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ تم تثبيت المتطلبات بنجاح")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        sys.exit(1)

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 إنشاء المجلدات...")
    
    directories = [
        'uploads',
        'instance',
        'logs'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ تم إنشاء مجلد: {directory}")
        else:
            print(f"📁 المجلد موجود: {directory}")

def setup_environment():
    """إعداد متغيرات البيئة"""
    print("🔧 إعداد البيئة...")
    
    env_file = '.env'
    if not os.path.exists(env_file):
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write("# إعدادات نظام إدارة الملفات\n")
            f.write("FLASK_CONFIG=development\n")
            f.write("SECRET_KEY=your-secret-key-change-in-production\n")
            f.write("DATABASE_URL=sqlite:///file_management.db\n")
        print(f"✅ تم إنشاء ملف البيئة: {env_file}")
    else:
        print(f"📄 ملف البيئة موجود: {env_file}")

def initialize_database():
    """تهيئة قاعدة البيانات"""
    print("🗄️ تهيئة قاعدة البيانات...")
    try:
        from app import create_app, db
        app = create_app()
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")
            
            # التحقق من وجود المدير الافتراضي
            from app.models import User, Department
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("👤 المدير الافتراضي موجود")
            else:
                print("👤 سيتم إنشاء المدير الافتراضي عند أول تشغيل")
                
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية للإعداد"""
    print("=" * 60)
    print("🚀 إعداد نظام إدارة الملفات")
    print("=" * 60)
    
    # التحقق من إصدار Python
    check_python_version()
    
    # تثبيت المتطلبات
    install_requirements()
    
    # إنشاء المجلدات
    create_directories()
    
    # إعداد البيئة
    setup_environment()
    
    # تهيئة قاعدة البيانات
    initialize_database()
    
    print("=" * 60)
    print("✅ تم إعداد النظام بنجاح!")
    print("=" * 60)
    print("🎯 الخطوات التالية:")
    print("1. تشغيل النظام: python run.py")
    print("2. فتح المتصفح: http://localhost:8080")
    print("3. تسجيل الدخول:")
    print("   - اسم المستخدم: admin")
    print("   - كلمة المرور: admin123")
    print("   - القسم: الإدارة")
    print("=" * 60)

if __name__ == '__main__':
    main()
