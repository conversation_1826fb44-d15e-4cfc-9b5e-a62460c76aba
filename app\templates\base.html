<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الملفات{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        .btn {
            border-radius: 0.375rem;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #fff;
            border-left: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #e9ecef;
            color: #495057;
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-left: 0.5rem;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .file-icon {
            font-size: 1.5rem;
            margin-left: 0.5rem;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stats-card-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .stats-card-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .stats-card-4 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        
        .file-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        
        .footer {
            background-color: #fff;
            border-top: 1px solid #dee2e6;
            padding: 1rem 0;
            margin-top: 2rem;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-folder-open me-2"></i>
                نظام إدارة الملفات
            </a>
            
            {% if current_user.is_authenticated %}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if current_user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('employee.dashboard') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            {% if not current_user.is_admin %}
                            <li><a class="dropdown-item" href="{{ url_for('employee.profile') }}">
                                <i class="fas fa-user-circle me-2"></i>الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        {% if current_user.is_admin %}
                        <!-- Admin Menu -->
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'admin.dashboard' }}" 
                               href="{{ url_for('admin.dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'departments' in request.endpoint }}" 
                               href="{{ url_for('admin.departments') }}">
                                <i class="fas fa-building"></i>
                                إدارة الأقسام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'users' in request.endpoint }}" 
                               href="{{ url_for('admin.users') }}">
                                <i class="fas fa-users"></i>
                                إدارة الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'files' in request.endpoint }}" 
                               href="{{ url_for('admin.files') }}">
                                <i class="fas fa-folder-open"></i>
                                جميع الملفات
                            </a>
                        </li>
                        {% else %}
                        <!-- Employee Menu -->
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'employee.dashboard' }}" 
                               href="{{ url_for('employee.dashboard') }}">
                                <i class="fas fa-home"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'employee.upload_file' }}" 
                               href="{{ url_for('employee.upload_file') }}">
                                <i class="fas fa-cloud-upload-alt"></i>
                                رفع ملف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'employee.my_files' }}" 
                               href="{{ url_for('employee.my_files') }}">
                                <i class="fas fa-folder"></i>
                                ملفاتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'employee.public_files' }}" 
                               href="{{ url_for('employee.public_files') }}">
                                <i class="fas fa-share-alt"></i>
                                الملفات العامة
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            
            <!-- Main content area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            {% else %}
            <!-- Full width for non-authenticated users -->
            <main class="col-12">
            {% endif %}
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="mt-3">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}
                
                <!-- Page Content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-auto">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12 text-center">
                    <small class="text-muted">
                        © 2024 نظام إدارة الملفات - جميع الحقوق محفوظة
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
