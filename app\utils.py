import os
import uuid
from functools import wraps
from flask import abort, current_app
from flask_login import current_user
from werkzeug.utils import secure_filename

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def allowed_file(filename):
    """التحقق من امتداد الملف المسموح"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def generate_unique_filename(filename):
    """إنشاء اسم ملف فريد"""
    # الحصول على الامتداد
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    # إنشاء اسم فريد
    unique_filename = str(uuid.uuid4()) + '.' + ext
    return unique_filename

def get_file_size(file_path):
    """الحصول على حجم الملف"""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def get_file_type(filename):
    """تحديد نوع الملف"""
    if not filename or '.' not in filename:
        return 'unknown'
    
    ext = filename.rsplit('.', 1)[1].lower()
    
    # تصنيف أنواع الملفات
    image_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg'}
    document_extensions = {'pdf', 'doc', 'docx', 'txt', 'rtf'}
    spreadsheet_extensions = {'xls', 'xlsx', 'csv'}
    presentation_extensions = {'ppt', 'pptx'}
    archive_extensions = {'zip', 'rar', '7z', 'tar', 'gz'}
    
    if ext in image_extensions:
        return 'image'
    elif ext in document_extensions:
        return 'document'
    elif ext in spreadsheet_extensions:
        return 'spreadsheet'
    elif ext in presentation_extensions:
        return 'presentation'
    elif ext in archive_extensions:
        return 'archive'
    else:
        return 'other'

def get_file_icon(file_type):
    """الحصول على أيقونة الملف حسب النوع"""
    icons = {
        'image': 'fas fa-image',
        'document': 'fas fa-file-alt',
        'spreadsheet': 'fas fa-file-excel',
        'presentation': 'fas fa-file-powerpoint',
        'archive': 'fas fa-file-archive',
        'other': 'fas fa-file'
    }
    return icons.get(file_type, 'fas fa-file')

def validate_file_upload(file, max_size=None):
    """التحقق من صحة الملف المرفوع"""
    if not file:
        return False, "لم يتم اختيار ملف"

    if not file.filename:
        return False, "اسم الملف فارغ"

    if not allowed_file(file.filename):
        return False, f"نوع الملف غير مسموح. الأنواع المسموحة: {', '.join(current_app.config['ALLOWED_EXTENSIONS'])}"

    # التحقق من حجم الملف إذا تم تحديد حد أقصى
    if max_size and hasattr(file, 'content_length') and file.content_length:
        if file.content_length > max_size:
            return False, f"حجم الملف كبير جداً. الحد الأقصى: {format_file_size(max_size)}"

    return True, "الملف صالح"

def clean_filename(filename):
    """تنظيف اسم الملف من الأحرف غير المرغوب فيها"""
    # إزالة المسارات والأحرف الخطيرة
    filename = secure_filename(filename)

    # استبدال المسافات بشرطات سفلية
    filename = filename.replace(' ', '_')

    return filename
